import os
import uuid
import json
import time
import random
import numpy as np
import pandas as pd
import itertools
from tqdm.auto import tqdm
from pinecone import Pinecone, ServerlessSpec
from langchain.text_splitter import RecursiveCharacterTextSplitter
from openai import AzureOpenAI
from services.cohere_embedding import CohereService
from concurrent.futures import ThreadPoolExecutor
import asyncio
import ssl


class CanopyAI:
    def __init__(self):
        # Get the path to the parent directory
        parent_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
        
        # Load environment variables from env.json file in the parent directory
        env_file_path = os.path.join(parent_dir, 'env.json')
        with open(env_file_path, 'r') as f:
            env_data = json.load(f)
        
        
        # Set up Pinecone client
        print(f"Pinecone api key: {env_data.get('PINECONE_API_KEY', '')}")
        self.pc = Pinecone(api_key=env_data.get("PINECONE_API_KEY", ""))

        # self.index = self.pc.Index("canopy-ai-energy-v3")
        self.index = self.pc.Index("aienergy-v1")
        
        # Set up Azure OpenAI client
        self.azure_client = AzureOpenAI(
            azure_endpoint=env_data.get("AZURE_ENDPOINT", ""),
            api_key=env_data.get("AZURE_API_KEY", ""),
            api_version=env_data.get("AZURE_API_VERSION", "")
        )
        self.deployments_map = {
            "gpt-3.5-turbo-16k": ["ai_energy-3-16k-1"],
            "gpt-4-turbo": ["ai_energy-4-turbo-1"],
            "gpt-3.5-turbo": ["ai_energy-3-turbo"],
            "text-embedding-ada-002": ["ai_energy_ada_2"]
        }
        
        self.cohere_service = CohereService()

    def create_index(self, index_name, dimension, metric):
        index_list = self.pc.list_indexes()
        indexes = [x["name"] for x in index_list]
        if index_name in indexes:
            return "exist"
        else:
            try:
                self.pc.create_index(
                    name=index_name,
                    dimension=dimension,
                    metric=metric,
                    spec=ServerlessSpec(
                        cloud="aws",
                        region="us-west-2"
                    )
                )
                while not self.pc.describe_index(index_name).status['ready']:
                    time.sleep(1)
                return "done"
            except Exception as e:
                return "error"

    def index_status(self, index_name):
        index = self.pc.Index(index_name)
        return index.describe_index_stats()

    def delete_index(self, index_name):
        return self.pc.delete_index(index_name)

    def get_metadata(self, data):
        return [{"title": x["title"], "content": x["content"], "source": x["source"], "documents": x["summary"]} for x in data]
    
    def get_metadata_v2(self, data,file_name,project_type):
        print(f"PROJECT TYPE : {project_type}")
        return [{"documents": x["content"], "section_id": x["section_id"], "title": x.get("title", ""),"file_name":file_name,"project_type":project_type} for x in data]

    def create_embedding_v2(self, text, model='sentence-transformers/all-MiniLM-L12-v2'):
        pass
        # print("creating embedding... \n")
        # model = SentenceTransformer(model)
        # embedding = model.encode(text)
        # embedding = np.array(embedding).tolist()
        # return embedding
    
    def create_embedding_v3(self, texts, input_type="search_query"):
        embeddings = self.cohere_service.embed_texts_as_list(texts, input_type=input_type)
        return embeddings

    def batch_embed(self, texts, batch_size=50):
        with ThreadPoolExecutor(max_workers=4) as executor:  # Adjust max_workers based on hardware
            futures = [executor.submit(self.cohere_service.embed_texts_as_list, texts[i:i + batch_size], input_type="search_document", model="embed-multilingual-v3.0") for i in range(0, len(texts), batch_size)]
            results = [future.result() for future in futures]
        return [item for sublist in results for item in sublist]

    def chunks(self, iterable, batch_size=200):
        """A helper function to break an iterable into chunks of size batch_size."""
        it = iter(iterable)
        chunk = tuple(itertools.islice(it, batch_size))
        while chunk:
            yield chunk
            chunk = tuple(itertools.islice(it, batch_size))
    
    def fetch_all_ids(self, index_name, namespace):
        print(index_name)
        index = self.pc.Index(index_name)
        resp = index.list(namespace=namespace)
        res = []
        for ids in resp:
            res += ids
        return res
    
    def fetch_all_data(self, index_name, namespace):
        index = self.pc.Index(index_name)
        ids = self.fetch_all_ids(index_name, namespace)
        l = 200
        res = []
        for i in range(0, len(ids), l):
            resp = index.fetch(ids = ids[i:i+l], namespace=namespace)
            for id in resp.vectors:
                data = resp.vectors[id]
                res.append({
                    "content": data['metadata']['documents'],
                    "section_id": data['metadata'].get('section_id', '')
                })
        return res
    
    def fecth_all_namespaces(self, index_name):
        index = self.pc.Index(index_name)
        
        return index.describe_index_stats()
        

    def save_to_db(self, project_id, data,file_name,project_type):
        index = self.index
        metadatas = self.get_metadata_v2(data,file_name,project_type)
        ids = [str(uuid.uuid4()) for x in range(0, len(data))]
        print("ids generation done...")
        contents = [x['content'] for x in data]
        

        # Process embeddings in batches of 80
        batch_size = 80
        all_embeddings = []
        for i in range(0, len(contents), batch_size):
            batch_contents = contents[i:i+batch_size]
            batch_embeddings = self.create_embedding_v3(batch_contents, "search_document")
            all_embeddings.extend(batch_embeddings)
            print(f"Processed embeddings batch {i//batch_size + 1}/{(len(contents) + batch_size - 1)//batch_size}")
        
        df = pd.DataFrame({
            "id": ids,
            "metadata": metadatas,
            "values": all_embeddings
        })
        print("done creating embedding for all text content...")
        try:
            print("inserting data...")
            data_to_upsert = [{"id": row['id'], "values": row['values'], "metadata": row['metadata']} for _, row in df.iterrows()]
            for batch in tqdm(self.chunks(data_to_upsert, batch_size=500), total=len(data_to_upsert) // 500):
                index.upsert(vectors=batch, namespace=project_id)
            return ids
        except Exception as e:
            print(f"Error saving to pinecone db: {str(e)}")
            return "error saving to pinecone db !!!"
        
    
    async def save_uploaded_doc_to_db(self, project_id, file_name, data):
        # Initialize the index once at setup time
        index = self.index

        ids = [str(uuid.uuid4()) for _ in range(len(data))]

        # Batch embedding generation
        embeddings = self.batch_embed(data, batch_size=50)
        print("Done creating embeddings...")

        data_to_upsert = [{"id": id, "values": value, "metadata": {"content": content}} for id, value, content in zip(ids, embeddings, data)]

        try:
            # Async batch upserts
            print("Inserting data....")

            await self.async_upsert(project_id, index, data_to_upsert, batch_size=500)

            print('done with insertion....')
            # return ids
        except Exception as e:
            print(f"Error: {e}")
            return "error saving to Pinecone DB!"
    
    async def async_upsert(self, project_id, index, vectors, batch_size=500):
        tasks = [
            asyncio.create_task(self.async_upsert_batch(project_id, index, batch))
            for batch in self.chunks(vectors, batch_size=batch_size)
        ]
        await asyncio.gather(*tasks)
    
    async def async_upsert_batch(self, project_id, index, batch):
        print('upserting batch...')
        print(f'THIS IS WHAT IS BEING UPLOADED TO THE PINECONE DATABASE {project_id}')
        try:
            result = index.upsert(batch, namespace=f"temp_{project_id}")  # Direct call without `to_thread`
            return result
        except Exception as e:
            print(f"Batch upsert failed: {e}")
            return None



    def get_data(self, project_id, queries, limit=3):
        # index = self.pc.Index("canopy-ai-energy-v3")
        index = self.pc.Index("aienergy-v1")
        # print(f"\033[94mCreating embeddings for queries...{queries}\033[0m")  # Debugging statement in blue
        # print(f"\033[94mQueries: {queries}\033[0m")  # Debugging statement in blue
        query_vectors = self.create_embedding_v3(queries, "search_query")
       
        results = []
        # print("Queries: ",queries)
        for query in query_vectors:
            #print(f"\033[94mQuerying index for vector: {query}\033[0m")  # Debugging statement in blue
            response = index.query(
                namespace=project_id,
                vector=query,
                top_k=limit
            )
            #print(f"\033[94mResponse for query {query}: {response}\033[0m")  # Debugging statement in blue
            results.append(response)
        
        ids = []
        # print("ids: ",ids)
        for i in range(len(results)):
            temp = []
            for j in range(len(results[i]["matches"])):
                temp.append(results[i]["matches"][j]["id"])
            ids.append(temp)
        
        
        ids = [[match["id"] for match in result["matches"]] for result in results]
        scores = [[match["score"] for match in result["matches"]] for result in results]
        metadata = [[item['metadata'] for item in index.fetch(ids=ids[i], namespace=project_id)['vectors'].values()] for i in range(len(ids))]
        
        final_results = {
            "ids": ids,
            "distances": scores,
            "metadatas": metadata,
            "uris": None,
            "data": None
        }
        #print("\033[94mFinal results prepared:\033[0m", final_results)  # Debugging statement in blue
        # print(final_results)
        return final_results

    def get_data_by_section_ids(self, project_id, queries, section_ids, limit=3):
        """
        Query the vector database for specific queries but only return results from specified sections.
        
        Args:
            project_id (str): The namespace/project ID to query
            queries (list): List of query strings to search for
            section_ids (list): List of section IDs to filter results by
            limit (int): Maximum number of results to return per query
            
        Returns:
            dict: Dictionary containing filtered results with ids, distances, and metadata
        """
        index = self.pc.Index("aienergy-v1")
        query_vectors = self.create_embedding_v3(queries, "search_query")
        
        results = []
        for query in query_vectors:
            # Query with a higher limit initially to ensure we get enough results after filtering
            response = index.query(
                namespace=project_id,
                vector=query,
                top_k=limit * 3,  # Query more results to account for filtering
                include_metadata=True
            )
            
            # Filter matches to only include those with matching section_ids
            filtered_matches = [
                match for match in response["matches"]
                if match["metadata"].get("section_id") in section_ids
            ][:limit]  # Take only the top 'limit' matches after filtering
            
            results.append({"matches": filtered_matches})
        
        # Process the filtered results
        ids = [[match["id"] for match in result["matches"]] for result in results]
        scores = [[match["score"] for match in result["matches"]] for result in results]
        metadata = [[match["metadata"] for match in result["matches"]] for result in results]
        
        final_results = {
            "ids": ids,
            "distances": scores,
            "metadatas": metadata,
            "uris": None,
            "data": None
        }
        
        return final_results

    def get_uploaded_data(self, project_id, queries, limit=3):
        # Connect to the Pinecone index
        # index = self.pc.Index("canopy-ai-energy-v3")
        index = self.pc.Index("aienergy-v1")
        
        # Generate embeddings for the queries
        query_vectors = self.create_embedding_v3(queries, input_type="search_query")
        
        # Prepare the results
        final_results = {"ids": [], "distances": [], "metadatas": []}

        # Query Pinecone for each query vector
        for query_vector in query_vectors:
            response = index.query(
                namespace=project_id,
                vector=query_vector,
                top_k=limit,
                include_metadata=True
            )

            # print('i am response: ', response)
            
            # Process the response
            ids = [match["id"] for match in response["matches"]]
            scores = [match["score"] for match in response["matches"]]
            metadata = [match["metadata"] for match in response["matches"]]
            
            # Append results
            final_results["ids"].append(ids)
            final_results["distances"].append(scores)
            final_results["metadatas"].append(metadata)
        
        # Return the compiled results
        return final_results

    def get_data_by_metadata_filter(self, project_id, queries, metadata_filter, limit=3):
        """
        Query the vector database for specific queries but only return results matching metadata filters.
        
        Args:
            project_id (str): The namespace/project ID to query
            queries (list): List of query strings to search for
            metadata_filter (dict): Dictionary of metadata key-value pairs to filter by (e.g., {"file_name": "xyz.pdf"})
            limit (int): Maximum number of results to return per query
            
        Returns:
            dict: Dictionary containing filtered results with ids, distances, and metadata
        """
        index = self.pc.Index("aienergy-v1")
        query_vectors = self.create_embedding_v3(queries, "search_query")
        
        results = []
        for query in query_vectors:
            # Query with Pinecone metadata filtering
            response = index.query(
                namespace=project_id,
                vector=query,
                top_k=limit,
                include_metadata=True,
                filter=metadata_filter
            )
            results.append(response)
        
        # Process the results
        ids = [[match["id"] for match in result["matches"]] for result in results]
        scores = [[match["score"] for match in result["matches"]] for result in results]
        metadata = [[match["metadata"] for match in result["matches"]] for result in results]
        
        final_results = {
            "ids": ids,
            "distances": scores,
            "metadatas": metadata,
            "uris": None,
            "data": None
        }
        
        return final_results

    def delete_namespace(self, project_id):
        # index = self.pc.Index("canopy-ai-energy-v3")
        index = self.pc.Index("aienergy-v1")
        return index.delete(delete_all=True, namespace=project_id)

    def chunk_text(self, text, chunk_size=512):
        text_splitter = RecursiveCharacterTextSplitter(
            chunk_size=chunk_size,
            chunk_overlap=20
        )
        docs = text_splitter.create_documents([text])
        return docs

    def create_batches(self, data, batch_size=200):
        return [data[i:i + batch_size] for i in range(0, len(data), batch_size)]

    def create_embedding(self, text):
        pass
    
    def move_embeddings(self, from_index_name, to_index_name):
        from_index = self.pc.Index(from_index_name)
        to_index = self.pc.Index(to_index_name)
        
        index_stats = from_index.describe_index_stats()
        for namespace in index_stats.namespaces:
            print("Moving namespace: ", namespace)
            try:
                to_index.delete(namespace)
            except Exception as e:
                print("Namespace already empty", e)
            data = self.fetch_all_data(from_index_name, namespace)
            self.save_to_db(namespace, data)
        pass

if __name__ == "__main__":
    pinecone_service = CanopyAI()
    query = pinecone_service.get_data('53dc535d-2325-4a5b-a01d-46e6dd3f53d5', ['punch list'])
    print(query)
    # res = pinecone_service.move_embeddings('canopy-ai-energy-v2', 'canopy-ai-energy-v3')
    # print(res)
    
    