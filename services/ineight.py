import gevent
import gevent.monkey
gevent.monkey.patch_all()

import argparse
import json
import requests
from datetime import datetime, timezone, timedelta
import sys
import os
from cryptography.fernet import Fernet
import urllib.parse

from models import Project, Transmittal_run, File
from dramatiq_file_broker import run_file_upload_task

class InEightClient:
    def __init__(self, fernet_key=None):
        self.session_key = None
        self.directory_data = None
        self.fernet_key = fernet_key
        self.ineight_info_dir = None
        
        self.initialize_data_directory()
        
    def set_fernet_key(self, fernet_key):
        self.fernet_key = fernet_key
        
    def initialize_data_directory(self):
        parent_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
        ineight_info_dir = os.path.join(parent_dir, 'data', 'ineight_info')
        self.ineight_info_dir = ineight_info_dir
        if not os.path.exists(ineight_info_dir):
            os.makedirs(ineight_info_dir)
            
    def create_download_folder(self, project_id):
        parent_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
        base_dir = os.path.join(parent_dir, 'data')
        os.makedirs(base_dir, exist_ok=True)

        download_dir = os.path.join(base_dir, str(project_id))
        os.makedirs(download_dir, exist_ok=True)

        print(f"Created download folder: {download_dir}")
        return download_dir
        
    def decrypt_value(self, cipher, val):
        return cipher.decrypt(val.encode()).decode() if val else None
    
    def allowed_file(self, filename):
        ALLOWED_EXTENSIONS = {'doc', 'docx', 'pdf', 'txt', "csv", "xls", "xlsx", "xlsm"}
        return '.' in filename and filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS
        
        
    def fetch_ineight_projects(self):
        try:
            cipher = Fernet(self.fernet_key.encode())
            all_projects = Project.get_by(is_ineight=1, user_id=30, entity_type="project")
            
            ineight_projects = [p for p in all_projects if p.get('is_ineight') == 1]
            
            if not ineight_projects:
                return []
                
            return ineight_projects, cipher
        except Exception as e:
            print(f"Error fetching iNeight projects: {e}")
            return [], None
        
        
    def fetch_project_transmittal_data(self):
        projects, cipher = self.fetch_ineight_projects()
        results = []
        
        for project in projects:
            try:
                ineight_project_id = self.decrypt_value(cipher, project.get('ineight_project_id'))
                ineight_user_id = self.decrypt_value(cipher, project.get('ineight_user_id'))
                ineight_company_id = self.decrypt_value(cipher, project.get('ineight_company_id'))
                ineight_password = self.decrypt_value(cipher, project.get('ineight_password'))
                
                if not all([ineight_project_id, ineight_user_id, ineight_company_id, ineight_password]):
                    results.append({
                        'project_id': project['id'],
                        'project_name': project['name'],
                        'status': 'error',
                        'error': 'Missing credentials'
                    })
                    continue
                
                env_config = {
                    'base_url': 'https://sa1.doc.ineight.com',  # Updated to sa1 instead of eu1
                    'userID': ineight_user_id,
                    'companyID': ineight_company_id,
                    'Password': ineight_password,
                    'ProjectNo': ineight_project_id,
                    'TfaToken': '.',  # Default value
                    'connectingProduct': '.',  # Default value
                    'Application': 'InEight Document'
                }
                # print("env_config >> ", env_config)
                session_key = self.get_session_key(env_config)
                
                if "ERROR" in session_key or not session_key:
                    raise ValueError(f"Session key error: {session_key}")
                
                directory_data = self.get_directory_data(session_key.strip('"'), env_config)
                
                # Check if existing transmittal file exists for this project
                existing_files = [f for f in os.listdir(self.ineight_info_dir) 
                                if f.startswith(f"transmittal_{project['id']}_") and f.endswith('.json')]
                
                # Delete any existing files for this project
                for existing_file in existing_files:
                    try:
                        os.remove(os.path.join(self.ineight_info_dir, existing_file))
                        print(f"Deleted old transmittal file: {existing_file}")
                    except Exception as e:
                        print(f"Error deleting old transmittal file {existing_file}: {e}")

                # Create new file with current timestamp
                timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
                filename = f"transmittal_{project['id']}_{timestamp}.json"
                file_path = os.path.join(self.ineight_info_dir, filename)
                
                with open(file_path, 'w') as f:
                    json.dump(directory_data, f, indent=2)
                
                print(f"Directory data for project {project['name']} saved to {file_path}")
                
                results.append({
                    'project_id': project['id'],
                    'project_name': project['name'],
                    'status': 'success',
                    'filename': filename
                })
                
            except Exception as e:
                print(f"Error processing project {project['name']}: {e}")
                results.append({
                    'project_id': project['id'],
                    'project_name': project['name'],
                    'status': 'error',
                    'error': str(e)
                })
        
        return results
            
    def get_session_key(self, env_config):
        base_url = "https://sa1.doc.ineight.com/tbws/session.asmx/LogonTFAWithApplication"
        url = (
            f"https://sa1.doc.ineight.com/tbws/session.asmx/LogonTFAWithApplication"
            f"?UserID={env_config['userID']}"
            f"&CompanyID={env_config['companyID']}"
            f"&TfaToken={env_config.get('TfaToken', '.')}"
            f"&ProjNo={env_config['ProjectNo']}"
            f"&connectingProduct={env_config.get('connectingProduct', '.')}"
            f"&Password={env_config['Password']}"
        )
        try:
            response = requests.get(url)
            if response.text.strip().startswith('<!DOCTYPE HTML') or '<html' in response.text.lower():
                return f"ERROR - Server returned HTML error response (status code: {response.status_code})"
            if response.status_code == 200:
                try:
                    # Parse the XML response to extract the session key
                    import xml.etree.ElementTree as ET
                    root = ET.fromstring(response.text)
                    session_key = root.text.strip() if root.text else None
                    
                    # Check if the response contains an error message
                    if session_key and "ERROR" in session_key:
                        return session_key  # Return the error message as is
                    
                    if session_key:
                        return session_key
                    else:
                        return "ERROR - No session key found in response"
                except ET.ParseError:
                    print(f"Failed to parse XML response: {response.text[:200]}...")
                    return f"ERROR - Invalid XML response from server"
            else:
                print(f"Failed to get session key. Status code: {response.status_code}")
                print(f"Response: {response.text}")
                return f"ERROR - Authentication failed (status code: {response.status_code})"
        except requests.RequestException as e:
            print(f"Request error getting session key: {e}")
            return f"ERROR - Network error: {str(e)}"
        except Exception as e:
            print(f"Unexpected error getting session key: {e}")
            return f"ERROR - Unexpected error: {str(e)}"
      
      
    def get_directory_data(self, session_key, env_config):
        """Get directory data using the session key"""
        directory_url = f"{env_config['base_url']}/TBReportingAPI/Report/04.001"
        headers = {
            'Accept': 'application/json',
            'Authorization': f'basic {session_key}'
        }

        try:
            response = requests.get(directory_url, headers=headers)
            
            # Check for HTML error responses
            if response.text.strip().startswith('<!DOCTYPE HTML') or '<html' in response.text.lower():
                print(f"Received HTML error response: {response.text[:200]}...")
                raise ValueError(f"Server returned HTML error response (status code: {response.status_code})")
            
            if response.status_code == 200:
                return response.json()
            else:
                print(f"Failed to get directory data. Status code: {response.status_code}")
                print(f"Response: {response.text}")
                raise ValueError(f"Failed to get directory data: {response.text}")
        except requests.RequestException as e:
            print(f"Request error getting directory data: {e}")
            raise ValueError(f"Network error: {str(e)}")
        except json.JSONDecodeError as e:
            print(f"JSON decode error: {e}")
            print(f"Response content: {response.text[:200]}...")
            raise ValueError(f"Invalid JSON response: {str(e)}")
        except Exception as e:
            print(f"Error getting directory data: {e}")
            raise ValueError(f"Unexpected error: {str(e)}")          
    
    def is_newer_revision(self, current_revision, previous_revision):
        """
        Helper function to determine if a new revision is newer than the current revision.

        Args:
            current_revision (str): The current revision string.
            previous_revision (str): The previous revision string.

        Returns:
            bool: True if the new revision is newer, False otherwise.
        """
        def revision_rank(rev):
            rev = rev.strip().upper()

            if rev == "PLH":
                return -2  # Lowest rank

            # Numeric revision (e.g., 0, 1, 2)
            if rev.isdigit():
                return int(rev)

            # Alphabetic revision (A, B, ... Z, AA, AB, ...)
            if rev.isalpha():
                rank = 0
                for c in rev:
                    rank = rank * 26 + (ord(c) - ord('A') + 1)
                return 1000 + rank  # Offset to ensure all letter ranks > numbers

            return float('inf')  # Unknown format is treated as highest (to be safe)

        return revision_rank(current_revision) > revision_rank(previous_revision)
       
    def remove_old_revisions(self, directory_data):
        """
        Filter directory data to keep only the newest revision of each document.
        
        Args:
            directory_data (list): List of document dictionaries from InEight.
            
        Returns:
            list: Filtered list with only the newest revision of each document.
        """
        # Group documents by DocumentNo
        document_groups = {}
        for item in directory_data:
            doc_no = item.get('DocumentNo')
            if doc_no not in document_groups:
                document_groups[doc_no] = []
            document_groups[doc_no].append(item)
        
        # For each group, keep only the newest revision
        filtered_data = []
        for doc_no, items in document_groups.items():
            if len(items) == 1:
                # Only one revision exists, keep it
                filtered_data.append(items[0])
            else:
                # Multiple revisions exist, find the newest one
                newest_item = items[0]
                for item in items[1:]:
                    if self.is_newer_revision(item.get('Rev', ''), newest_item.get('Rev', '')):
                        newest_item = item
                filtered_data.append(newest_item)
        
        print(f"Filtered {len(directory_data)} documents to {len(filtered_data)} (removed {len(directory_data) - len(filtered_data)} older revisions)")
        return filtered_data
     
    def handle_download(self):
        print("Starting ineight_download_handle...")
        results = []
        
        try:
            
            cipher = Fernet(self.fernet_key.encode())
            
            ineight_info_dir = self.ineight_info_dir
            
            # Get all JSON files in the directory
            json_files = [f for f in os.listdir(ineight_info_dir) if f.endswith('.json') and f.startswith('transmittal_')]
            
            if not json_files:
                print(f"No transmittal JSON files found in {ineight_info_dir}")
                return [{'status': 'error', 'error': 'No transmittal JSON files found'}]
            
            print(f"Found {len(json_files)} transmittal files")
            
            # Process each JSON file
            for json_file in json_files:
                print("json_file >> ", json_file)
                try:
                    # Extract project ID from filename (format: transmittal_PROJECTID_TIMESTAMP.json)
                    parts = json_file.split('_')
                    if len(parts) >= 2:
                        project_id = parts[1]
                        
                        # Fetch project details from database
                        project = Project.get_single(project_id)
                        
                        if not project:
                            print(f"Project with ID {project_id} not found in database")
                            results.append({
                                'file': json_file,
                                'project_id': project_id,
                                'status': 'error',
                                'error': 'Project not found in database'
                            })
                            continue
                        
                        # Read the JSON file
                        directory_data = []
                        file_path = os.path.join(ineight_info_dir, json_file)
                        with open(file_path, 'r') as f:
                            directory_data = json.load(f)
                            
                        directory_data = self.remove_old_revisions(directory_data)
                        
                        # Decrypt credentials for this project
                     
                        
                        ineight_project_id = self.decrypt_value(cipher, project.get('ineight_project_id'))
                        ineight_user_id = self.decrypt_value(cipher, project.get('ineight_user_id'))
                        ineight_company_id = self.decrypt_value(cipher, project.get('ineight_company_id'))
                        ineight_password = self.decrypt_value(cipher, project.get('ineight_password'))
                        
                        # Skip if any credential is missing
                        if not all([ineight_project_id, ineight_user_id, ineight_company_id, ineight_password]):
                            print(f"Skipping project {project['name']} due to missing credentials")
                            results.append({
                                'file': json_file,
                                'project_id': project_id,
                                'project_name': project.get('name', 'Unknown'),
                                'status': 'error',
                                'error': 'Missing credentials'
                            })
                            continue
                        
                        # Create environment config for this project
                        env_config = {
                            'base_url': 'https://sa1.doc.ineight.com',
                            'userID': ineight_user_id,
                            'companyID': ineight_company_id,
                            'Password': ineight_password,
                            'ProjectNo': ineight_project_id,
                            'TfaToken': '.',
                            'connectingProduct': '.',
                            'Application': 'InEight Document'
                        }
                        
                        # Get session key for this project
                        session_key = self.get_session_key(env_config)
                        # print("session_key >>", session_key)
                        # Check if session key contains an error message
                        if isinstance(session_key, str) and "ERROR" in session_key:
                            print(f"Session key error for project {project['name']}: {session_key}")
                            results.append({
                                'file': json_file,
                                'project_id': project_id,
                                'project_name': project.get('name', 'Unknown'),
                                'status': 'error',
                                'error': f'Authentication failed: {session_key}'
                            })
                            continue
                        
                        # Create project download folder
                        download_dir = self.create_download_folder(project_id)
                        
                        # Download files
                        print(f"Starting file downloads for project {project['name']}...")
                        self.download_file_from_ineight(directory_data, download_dir, session_key.strip('"'), env_config, project_id)
                        
                        # Ingest files
                        print(f"Starting file ingestion for project {project['name']}...")
                        process_result = self.process_files_from_directory(project, directory_data)
                        print("process_result >> ", process_result)
                        # ingest_download_files(directory_data, download_dir)
                        
                        results.append({
                            'file': json_file,
                            'project_id': project_id,
                            'project_name': project.get('name', 'Unknown'),
                            'status': 'success',
                            'message': f'Processed {len(directory_data)} documents',
                            # "process_result": process_result
                        })
                        
                    else:
                        print(f"Invalid filename format: {json_file}")
                        results.append({
                            'file': json_file,
                            'status': 'error',
                            'error': 'Invalid filename format'
                        })
                
                except Exception as e:
                    print(f"Error processing file {json_file}: {e}")
                    import traceback
                    traceback.print_exc()
                    results.append({
                        'file': json_file,
                        'status': 'error',
                        'error': str(e)
                    })
            
            return results
    
        except Exception as e:
            print(f"Error in ineight_download_handle: {e}")
            return [{'status': 'error', 'error': f'System error: {str(e)}'}]
        
        
    def get_download_url(self, session_key, int_key, env_config):
        
        # url = 'https://eu1.doc.ineight.com/tbws/document.asmx/GetDownloadLink'
        url = f"{env_config['base_url']}/tbws/document.asmx/GetDownloadLink"
        
        headers = {
            'Content-Type': 'application/x-www-form-urlencoded'
        }
        
        data = {
            'sessionKey': session_key,
            'int_viewFile': str(int_key)
        }
        
        try:
            response = requests.post(url, headers=headers, data=data)
            response.raise_for_status()
            
            # Parse XML response
            root = ET.fromstring(response.text)
            
            # Extract download URL from XML
            download_url = root.text
            
            if not download_url:
                raise ValueError("No download URL found in response")
                
            return download_url
            
        except requests.RequestException as e:
            print(f"Failed to get download URL: {str(e)}")
            raise
        except (ET.ParseError, ValueError) as e:
            print(f"Failed to parse response: {str(e)}")
            raise

    def get_file_extension(self,content_disposition, default_ext=".bin"):
        """
        Extract the file extension from the Content-Disposition header.
        Defaults to .bin if unknown.
        """
        if content_disposition:
            parts = content_disposition.split(";")
            for part in parts:
                if "filename=" in part:
                    filename = part.split("=")[-1].strip().strip('"')
                    return Path(filename).suffix if '.' in filename else default_ext
        return default_ext

    def download_file_from_ineight(self, directory_list, download_dir, session_key, env_config, project_id):
        transmittal_run_id = 1
        last_time= 'Thu, 27 Mar 2025 18:17:56 GMT'
    
            
        current_time_utc = datetime.now(timezone.utc)
        desired_timezone = timezone(timedelta(hours=2))  # UTC+2
        converted_time = current_time_utc.astimezone(desired_timezone)# Get the current time

        Transmittal_run.update(transmittal_run_id, last_time=converted_time) 
        
        for item in directory_list:
            try:
                file_name = item['DocumentNo'] + "_" + str(item['Int_Key']) + ".pdf"
                file = File.get_by(name=file_name, project_id=project_id)
                if (file and len(file) > 0) and not self.is_newer_revision(item.get('Rev', None), file[0]['revision']):
                    print(f"Skipping download for {file_name}, Rev {item.get('Rev', None)} is older or same as {file[0]['revision']}")
                    return
                            
                # try:
                download_url = self.get_download_url(session_key, item['Int_Key'], env_config)
                print(f"Download URL: {download_url}")
                # except Exception as e:
                #     print(f"Error getting download URL for {item.get('DocumentNo', 'Unknown')}: {str(e)}")
                #     continue
                
                doc_no = item['DocumentNo'].strip()
                Int_Key = item['Int_Key']
                
                # Download the file with streaming to handle large files
                response = requests.get(download_url, stream=True)
                response.raise_for_status()
                
                # Determine file extension from response headers
                content_disposition = response.headers.get('Content-Disposition', '')
                file_ext = self.get_file_extension(content_disposition, default_ext=".pdf")
                file_path = os.path.join(download_dir, f"{doc_no}_{Int_Key}{file_ext}")
                
                # Get total file size if available
                total_size = int(response.headers.get('content-length', 0))
                
                # Open file in binary write mode
                with open(file_path, 'wb') as f:
                    if total_size == 0:
                        # No content length header - just download
                        f.write(response.content)
                    else:
                        # Download with progress tracking for larger files
                        downloaded = 0
                        for chunk in response.iter_content(chunk_size=8192):
                            if chunk:
                                f.write(chunk)
                                downloaded += len(chunk)
                                # Calculate progress percentage
                                progress = int((downloaded / total_size) * 100)
                                print(f"\rDownloading {doc_no}: {progress}%", end='')
                        print()  # New line after progress
                
                print(f"Successfully downloaded {doc_no} to {file_path}")
                
            except Exception as e:
                import traceback
                traceback.print_exc()
                print(f"Error downloading {item['DocumentNo']}: {str(e)}")
                continue
                
            time.sleep(1)  # 1 second delay between downloads
            
    def process_files_from_directory(self, project, directory_data):
        try:
            parent_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
            UPLOAD_FOLDER = os.path.join(parent_dir, 'data')
            project_id = str(project['id'])
            if not project_id:
                print("Error: Project ID is required")
                return {"success": False, "message": "Project ID is required"}

            # Define the source directory
            # base_dir = os.getcwd()
            folder_path = os.path.join(UPLOAD_FOLDER, project_id)

            if not os.path.exists(folder_path):
                print(f"Error: Directory {folder_path} does not exist")
                return {"success": False, "message": f"Directory {folder_path} does not exist"}


            files = []
            for f in os.listdir(folder_path):
                if os.path.isfile(os.path.join(folder_path, f)) and self.allowed_file(f):
                    files.append(f)

            if not files:
                print("Error: No allowed files found in the directory")
                return {"success": False, "message": "No allowed files found in the directory"}

            result = []
            file_ids = []
            file_names = []
            vendor_file_ids = []
            vendor_file_names = []
            engineer_file_ids = []
            engineer_file_names = []
            all_success = True
            all_failed = True
            lock = threading.Lock()
            

            def process_file(entity, file_name, metadata):
                # with app.app_context():
                nonlocal all_success, all_failed
                file_path = os.path.join(folder_path, file_name)
                project_id = entity['id']
                try:
                    existing_file = File.get_by(name=file_name, project_id=project_id)
                    if existing_file and len(existing_file) > 0:
                        existing_file = existing_file[0] or None
                        
                        if not self.is_newer_revision(metadata.get('Rev', None), existing_file['revision']):
                            print(f"Skipping processing for {file_name}, Rev {metadata.get('Rev', None)} is older or same as {existing_file['revision']}")
                            return
                            
                        
                        if existing_file['revision'] != metadata.get('Rev', None):
                            File.update(existing_file['id'], revision=metadata.get('Rev', None))
                        
                        file_ids.append({'id': existing_file['id'], 'entity': entity['entity_type']})
                        file_names.append({'name': file_name, 'entity': entity['entity_type']})
                        result.append({"success": True, "file": file_name})
                        return
                        
                    file_id = str(uuid.uuid4()) 
                    file_extension = file_name.rsplit('.', 1)[-1].lower()
                    pdf_conversion_status = 'done' if file_extension == 'pdf' else 'idle'
                    UPLOAD_DIR = "data"

                    file_data = {
                        'id': file_id,
                        'name': file_name,
                        'project_id': project_id,
                        'file_dirtry': UPLOAD_DIR,
                        'file_type': file_extension,
                        'pdf_conversion_status': pdf_conversion_status,
                        'revision': metadata.get('Rev', None)
                    }

                    File.create(**file_data)

                    # with lock:
                    all_failed = False
                    file_ids.append({'id': file_id, 'entity': entity['entity_type']})
                    file_names.append({'name': file_name, 'entity': entity['entity_type']})
                    result.append({"success": True, "file": file_name})

                    print(f"Successfully processed file: {file_name}")
                except Exception as e:
                    # with lock:
                    all_success = False
                    result.append({"success": False, "file": file_name, "error": str(e)})
                    import traceback
                    traceback.print_exc()
                    print(f"Failed to process file {file_name}: {str(e)}")

            for file in files:
                # Extract DocumentNo and Int_Key from filename
                # Format is "DocumentNo_Int_Key.extension" where DocumentNo can contain underscores
                try:
                    base_name = os.path.splitext(file)[0]  # Remove extension
                    parts = base_name.split('_')
                    if len(parts) >= 2:
                        # Last part is Int_Key, rest is DocumentNo
                        int_key = int(parts[-1])  # Convert last part to integer
                        document_no = '_'.join(parts[:-1])  # Join all parts except last
                        print(f"Processing file {file}  DocumentNo: {document_no} Int_Key: {int_key}")
                        
                        # Find matching metadata in directory_data
                        metadata = next(
                            (item for item in directory_data 
                             if item['DocumentNo'] == document_no and item['Int_Key'] == int_key),
                            None
                        )
                        
                        if not metadata:
                            continue
                            print(f"Warning: No metadata found for file {file}")
                            metadata = {}
                    else:
                        print(f"Warning: Invalid filename format for {file}")
                        metadata = {}
                except Exception as e:
                    print(f"Error extracting metadata for {file}: {str(e)}")
                    metadata = {}
                
                file_entity = self.get_file_entity(project_id, metadata.get('Category', None))
                # print("file_entity >> ", file_entity)
                process_file(file_entity, file, metadata)

            message = (
                "All files successfully processed" if all_success else
                "All files failed to process" if all_failed else
                "One or more files failed to process"
            )

            print(f"Processing complete: {message}")
            print(f"folder_path: {folder_path}")
            print(f"UPLOAD_FOLDER: {UPLOAD_FOLDER}")
            
            vendor_file_ids = [file['id'] for file in file_ids if file['entity'] == 'vendor']
            vendor_file_names = [file['name'] for file in file_names if file['entity'] == 'vendor']
            engineer_file_ids = [file['id'] for file in file_ids if file['entity'] == 'engineer']
            engineer_file_names = [file['name'] for file in file_names if file['entity'] == 'engineer']
            
            other_file_ids = [file['id'] for file in file_ids if file['entity'] == 'project']
            other_file_names = [file['name'] for file in file_names if file['entity'] == 'project']
            if len(vendor_file_ids) > 0:
                vendor_entity_id = self.get_file_entity_id(project_id, 'vendor')
                self.move_file_to_entity(UPLOAD_FOLDER, vendor_file_names, project_id, vendor_entity_id)
                # run_file_upload_task.send(UPLOAD_FOLDER, vendor_entity_id, vendor_file_ids, vendor_file_names, None)
            if len(engineer_file_ids) > 0:
                engineer_entity_id = self.get_file_entity_id(project_id, 'engineer')
                self.move_file_to_entity(UPLOAD_FOLDER, engineer_file_names, project_id, engineer_entity_id)
                # run_file_upload_task.send(UPLOAD_FOLDER, engineer_entity_id, engineer_file_ids, engineer_file_names, None)
            if len(other_file_ids) > 0:
                pass
                # run_file_upload_task.send(UPLOAD_FOLDER, project_id, other_file_ids, other_file_names, None)

            return {
                "message":message, 
                "result":result
            }

        except Exception as e:
            traceback.print_exc()
            print(f"Error in process_files_from_directory: {str(e)}")
            return {"success": False, "message": f"System error: {str(e)}"}

    def get_file_entity(self, project_id, category="DOC"):
        project = Project.get_single(project_id)
        
        
        # Determine entity type based on category
        if category == 'VEN':
            entity_type = 'vendor'
        elif category == 'EDD':
            entity_type = 'engineer'
        else:  # Default to DOC
            return project
        
        # Check if entity already exists
        try:
            existing_entity = Project.get_one_by(
                parent_id=project_id,
                entity_type=entity_type
            )
        except Exception as e:
            print(f"Error in get_file_entity: {str(e)}")
            existing_entity = None
        
        if existing_entity:
            return existing_entity.serialize
        
        # Create new entity if it doesn't exist
        new_entity = Project.create(
            id=str(uuid.uuid4()),
            name=f"{project['name']} {entity_type.capitalize()}",
            entity_type=entity_type,
            parent_id=project_id,
            is_ineight=1,  # Mark as iNeight entity
            user_id=project['user_id']
        )
        
        if not new_entity:
            raise Exception("Failed to create new entity")
            
        return new_entity
    
    def get_file_entity_id(self, project_id, entity_type):
        if entity_type == 'vendor':
            return Project.get_one_by(parent_id=project_id, entity_type='vendor').serialize['id']
        elif entity_type == 'engineer':
            return Project.get_one_by(parent_id=project_id, entity_type='engineer').serialize['id']
        else:       
            return project_id
        
    def move_file_to_entity(self, UPLOAD_FOLDER, files, project_id, entity_id):
        for file in files:
            file_path = os.path.join(UPLOAD_FOLDER, project_id, file)
            entity_path = os.path.join(UPLOAD_FOLDER, entity_id)
            os.makedirs(entity_path, exist_ok=True)
            import shutil
            shutil.copy(file_path, entity_path)

def read_env_file(env_path):
    """Read and parse the environment JSON file"""
    try:
        with open(env_path, 'r') as f:
            return json.load(f)
    except Exception as e:
        print(f"Error reading environment file: {e}")
        sys.exit(1)


def save_directory_data(data):
    """Save directory data to JSON file"""
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    filename = f"transmittal_report.json"
    
    # Create ineight_info directory if it doesn't exist
    ineight_info_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'ineight_info')
    os.makedirs(ineight_info_dir, exist_ok=True)
    
    file_path = os.path.join(ineight_info_dir, filename)
    
    try:
        with open(file_path, 'w') as f:
            json.dump(data, f, indent=2)
        print(f"Directory data saved to {file_path}")
    except Exception as e:
        print(f"Error saving directory data: {e}")
        raise ValueError(f"Failed to save directory data: {str(e)}")

def transmittal_daily_work(FERNET_KEY):
    try:
        # Process iNeight projects
        ineight_client = InEightClient(fernet_key=FERNET_KEY)
        
        data = ineight_client.fetch_project_transmittal_data()
        return data
    except Exception as e:
        print(f"Error in transmittal_daily_work: {e}")
        # Return error information instead of crashing
        return [{'status': 'error', 'error': f'System error: {str(e)}'}]
    
    

import argparse
import json
import os
import time
from pathlib import Path
import requests
import xml.etree.ElementTree as ET
import sys

from pinecone import Pinecone, ServerlessSpec
from langchain.embeddings.openai import OpenAIEmbeddings
from langchain.text_splitter import RecursiveCharacterTextSplitter
from concurrent.futures import ThreadPoolExecutor
import os
from datetime import datetime
from dateutil import parser
from cryptography.fernet import Fernet
import uuid
import traceback
import threading

# Get the parent directory (one level up)
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), "..")))

from services.cohere_embedding import CohereService


parent_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
        
# Load environment variables from env.json file in the parent directory
env_file_path = os.path.join(parent_dir, 'env.json')
with open(env_file_path, 'r') as f:
    env_data = json.load(f)
    
# pinecone.init(api_key=env_data.get("PINECONE_API_KEY", ""), environment=PINECONE_ENV)
pc = Pinecone(api_key=env_data.get("PINECONE_API_KEY", ""), environment="us-east-1")
# openai.api_key = env_data.get("OPENAI_API_KEY", "")
# embed_model = OpenAIEmbeddings(openai_api_key=env_data.get("OPENAI_API_KEY", ""))
embed_model = CohereService()


# Create or connect to Pinecone index
# pc.delete_index("transmitall-document")
index_name = "transmitall-document"
if index_name not in pc.list_indexes().names():
    pc.create_index(
        name=index_name,
        dimension=1024,  # Adjust as needed
        metric="cosine",
        spec=ServerlessSpec(cloud="aws", region="us-east-1")  # Change region accordingly
    )

index = pc.Index(index_name)

text_splitter = RecursiveCharacterTextSplitter(
    chunk_size=500,
    chunk_overlap=50,
    length_function=len,
    separators=["\n\n", "\n", " ", ""]
)

ALLOWED_EXTENSIONS = {'doc', 'docx', 'pdf', 'txt', "csv", "xls", "xlsx", "xlsm"}
def allowed_file(filename):
    return '.' in filename and filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

def read_env_file(env_path):
    """Read and parse the environment JSON file"""
    try:
        with open(env_path, 'r') as f:
            return json.load(f)
    except Exception as e:
        print(f"Error reading environment file: {e}")
        sys.exit(1)

def parse_args():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(description='Download and ingest files from InEight')
    parser.add_argument('--dir', required=True, help='Path to directory.json file')
    parser.add_argument('--env', required=True, help='Path to environment JSON file')
    return parser.parse_args()





if __name__ == "__main__":
    from init import app
    from models import Project, File
    
    parent_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))

    env_file_path = os.path.join(parent_dir, 'env.json')
    env_data = {}
    with open(env_file_path, 'r') as f:
        env_data = json.load(f)
    
    with app.app_context():
        ineight_client = InEightClient(fernet_key=env_data.get("FERNET_KEY", ""))
        data = ineight_client.fetch_project_transmittal_data()
        ineight_client.handle_download()


# Category - VEN (vendor), Project (DOC), Engineering (EDD)
