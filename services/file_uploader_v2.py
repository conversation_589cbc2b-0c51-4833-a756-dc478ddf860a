from services.process_pdf_ocr import process_df_with_ocr_support
import asyncio
import uuid
import os
import threading
from werkzeug.utils import secure_filename
from services.docx_to_pdf_v2 import DocxToPdf
from services.document_seeder import DocumentSeeder
from models import File
from concurrent.futures import ThreadPoolExecutor
from models import Requirement
import colorama
from colorama import Fore, Style

class Fileuploader:

    def __init__(self, dirtry, project_id, file_ids, file_names, pdf_concurrency=1, flask_app=None, socket_manager=None, req_id=None, is_retry=False,project_type="") -> None:
        """
        Initialize the Fileuploader with configurable PDF concurrency
        
        Args:
            dirtry: Directory for file storage
            project_id: Project identifier
            file_ids: List of file IDs
            file_names: List of file names
            pdf_concurrency: Number of PDF files to process concurrently (default: 1)
            flask_app: Flask application instance (required for Celery worker)
            socket_manager: Socket manager instance for real-time updates
            req_id: Request ID for socket communication
            is_retry: Boolean indicating if this is a retry operation
        """
        colorama.init()  # Initialize colorama for colored output
        self.file_dirtry = dirtry
        self.folder_path = os.path.join(dirtry, project_id)
        os.makedirs(self.folder_path, mode=0o775, exist_ok=True)
        self.file_ids = file_ids
        self.file_names = file_names
        self.pdf_concurrency = max(1, pdf_concurrency)  # Ensure at least 1
        self.all_success = True
        self.all_failed = True
        self.lock = threading.Lock()
        # self.doc_to_pdf_processor = DocxToPdf()
        self.document_seeder_processor = DocumentSeeder()
        self.thread_pool = ThreadPoolExecutor(max_workers=5)
        self.flask_app = flask_app  # Store Flask app reference
        self.socket_manager = socket_manager
        self.req_id = req_id  # Store request ID for socket communication
        self.original_requirement = {}
        self.is_retry = is_retry 
        self.project_type = project_type # Store whether this is a retry operation

    def add_event(self, event_name, data):
        """
        Emit socket event to client for progress tracking.
        
        Args:
            event_name: Name of the event
            data: Data to be sent with the event
        """
        if self.socket_manager and self.req_id:
            self.log_info(f"Emitting {event_name} event to room {self.req_id}")
            self.socket_manager.emit_to_client(
                self.req_id, 
                event_name, 
                data,
                namespace='/file_upload'
            )

    def uploader(self):
        self.log_info('Starting background processing for file uploader...')
        # Start background processing in a separate thread
        self.background_processing()

    def log_info(self, message):
        """Print information messages in blue"""
        print(f"{Fore.BLUE}{message}{Style.RESET_ALL}")
        # Also send as socket event if socket manager is available
      

    def log_success(self, message):
        """Print success messages in green"""
        print(f"{Fore.GREEN}{message}{Style.RESET_ALL}")
        # Also send as socket event if socket manager is available
       

    def log_error(self, message):
        """Print error messages in red"""
        print(f"{Fore.RED}{message}{Style.RESET_ALL}")
        # Also send as socket event if socket manager is available
        
    def background_processing(self):
        """Handle all background processing tasks"""
        try:
            # For Celery workers, use the provided Flask app
            if self.flask_app:
                with self.flask_app.app_context():
                    self._process_files_internal()
            else:
                # For web requests, use current_app
                from flask import current_app
                with current_app.app_context():
                    self._process_files_internal()
                
        except Exception as e:
            self.log_error(f"Error in background processing: {str(e)}")
            import traceback
            traceback.print_exc()
            # Notify client of error
            if self.socket_manager and self.req_id:
                self.add_event('error_event', {
                    'event_type': 'file_upload',
                    'request_id': self.req_id,
                    'data': {'message': f"Error in  starting file upload processing: {str(e)}"}
                })
        finally:
            self.thread_pool.shutdown(wait=False)

    def _process_files_internal(self):
        """Internal method to process files within Flask context"""
        self.log_info('Converting idle docx to pdf before upload.....')
        # self.doc_to_pdf_processor.convert_idle_docx_files()

        self.log_info("Started background processing of files.")
        
        # Notify client that processing has started
        if self.socket_manager and self.req_id:
            self.add_event('processing_started', {
                'event_type': 'file_upload',
                'request_id': self.req_id,
                'data': {'message': "Started background processing of files."}
            })
        
        # Create a new event loop for this thread
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        
        # Separate files into PDF and non-PDF
        pdf_files = []
        non_pdf_files = []
        
        for idx, (file_id, file_name) in enumerate(zip(self.file_ids, self.file_names)):
            file_type = os.path.splitext(file_name)[-1].lower().lstrip('.')
            if file_type == 'pdf':
                pdf_files.append((file_id, file_name))
            else:
                non_pdf_files.append((file_id, file_name))
        
        self.log_info(f"Found {len(pdf_files)} PDF files and {len(non_pdf_files)} non-PDF files")
        
        async def process_files():
            # First, process all non-PDF files in parallel
            if non_pdf_files:
                self.log_info(f"Processing {len(non_pdf_files)} non-PDF files in parallel...")
                self.add_event('progress_message', {
                    'event_type': 'file_upload',
                    'request_id': self.req_id,
                    'data': {'status': 'processing', 'message': f"Processing a total of  {len(non_pdf_files)} non-PDF files in parallel..."}
                })
                
                non_pdf_tasks = []
                for idx, (file_id, file_name) in enumerate(non_pdf_files):
                    non_pdf_tasks.append(self.process_single_file(file_id, file_name, "non-pdf", len(non_pdf_files), idx))
                await asyncio.gather(*non_pdf_tasks)
                self.log_success("All non-PDF files processed successfully.")
            
            # Then, process PDF files with configured concurrency
            if pdf_files:
                self.log_info(f"Processing {len(pdf_files)} PDF files with concurrency of {self.pdf_concurrency}...")
                self.add_event('progress_message', {
                    'event_type': 'file_upload',
                    'request_id': self.req_id,
                    'data': {'status': 'processing', 'message': f"Processing  a total of {len(pdf_files)} PDF files with concurrency of {self.pdf_concurrency}..."}
                })
                
                # Process PDFs in batches based on concurrency setting
                for i in range(0, len(pdf_files), self.pdf_concurrency):
                    batch = pdf_files[i:i + self.pdf_concurrency]
                    batch_num = i//self.pdf_concurrency + 1
                    total_batches = (len(pdf_files) + self.pdf_concurrency - 1)//self.pdf_concurrency
                    
                    self.log_info(f"Starting PDF batch {batch_num} of {total_batches}")
                    self.add_event('progress_message', {
                        'event_type': 'file_upload',
                        'request_id': self.req_id,
                        'data': {
                            'status': 'processing', 
                            'message': f"Starting PDF batch {batch_num} of {total_batches}",
                            'progress': (batch_num - 1) / total_batches * 100
                        }
                    })
                    
                    pdf_tasks = []
                    for idx, (file_id, file_name) in enumerate(batch):
                        global_idx = i + idx
                        pdf_tasks.append(self.process_single_file(file_id, file_name, "pdf", len(pdf_files), global_idx))
                    
                    await asyncio.gather(*pdf_tasks)
                    self.log_info(f"PDF batch {batch_num} complete")
                    self.add_event('progress_message', {
                        'event_type': 'file_upload',
                        'request_id': self.req_id,
                        'data': {
                            'status': 'processing', 
                            'message': f"PDF batch {batch_num} complete",
                            'progress': batch_num / total_batches * 100
                        }
                    })
                
                self.log_success("All PDF files processed successfully.")
               

        # Run the async function in the event loop
        loop.run_until_complete(process_files())
        loop.close()
        self.log_success("✓ Background file processing complete.")
        
        # Notify client that processing is complete
        if self.socket_manager and self.req_id:
            self.add_event('processing_complete', {
                'event_type': 'file_upload',
                'request_id': self.req_id,
                'data': {'message': "Background file processing complete."}
            })

    async def process_single_file(self, file_id, file_name, file_category, total_files, file_index=None):
        """
        Process a single file and log progress
        
        Args:
            file_id: ID of the file to process
            file_name: Name of the file
            file_category: Category of file (pdf or non-pdf)
            total_files: Total number of files in this category
            file_index: Index of this file (for progress reporting)
        """
        try:
            # Determine file type
            file_type = os.path.splitext(file_name)[-1].lower().lstrip('.')
            
            if file_index is not None:
                progress_msg = f"Processing {file_category.upper()} file {file_index + 1}/{total_files}: {file_name}"
                progress_percent = (file_index + 1) / total_files * 100
            else:
                progress_msg = f"Processing {file_category.upper()} file: {file_name}"
                progress_percent = None
                
            self.log_info(progress_msg)
            
            # Send progress update via socket
            if self.socket_manager and self.req_id:
                self.add_event('file_progress', {
                    'event_type': 'file_upload',
                    'request_id': self.req_id,
                    'data': {
                        'status': 'processing', 
                        'message': progress_msg,
                        'file_name': file_name,
                        'file_id': file_id,
                        'progress': progress_percent
                    }
                })
            
            # Process the document using the seeder
            await self.document_seeder_processor.handle_single_document(file_id)
            
            if file_index is not None:
                success_msg = f"✓ Completed {file_category.upper()} file {file_index + 1}/{total_files}: {file_name}"
            else:
                success_msg = f"✓ Completed {file_category.upper()} file: {file_name}"
                
            self.log_success(success_msg)
            
            # Send completion update via socket
            if self.socket_manager and self.req_id:
                self.add_event('file_complete', {
                    'event_type': 'file_upload',
                    'request_id': self.req_id,
                    'data': {
                        'status': 'success', 
                        'message': success_msg,
                        'file_name': file_name,
                        'file_id': file_id
                    }
                })
            
        except Exception as e:
            error_msg = f"✗ Error processing file {file_name}: {str(e)}"
            self.log_error(error_msg)
            
            # Send error update via socket
            if self.socket_manager and self.req_id:
                self.add_event('file_error', {
                    'event_type': 'file_upload',
                    'request_id': self.req_id,
                    'data': {
                        'status': 'error', 
                        'message': error_msg,
                        'file_name': file_name,
                        'file_id': file_id,
                        'error': str(e)
                    }
                })
            
    def process_documents(self):
        try:
            # Initialize counters for successful and failed files
            self.successful_files = []
            self.failed_files = []
            
            # Notify client that processing has started with retry information if applicable
            operation_type = "retry processing" if self.is_retry else "processing"
            message = f"Processing..."
            
            if self.is_retry:
                message = f"RETRY OPERATION: {message}"
            
            self.add_event('processing_started', {
                'event_type': 'file_upload',
                'request_id': self.req_id,
                'data': {
                    'message': message,
                    'is_retry': self.is_retry
                }
            })
            
            # Use the provided Flask app if available
            context_manager = self.flask_app.app_context() if self.flask_app else None
            
            if context_manager is None:
                # Fall back to current_app for direct Flask requests
                from flask import current_app
                context_manager = current_app.app_context()
            
            with context_manager:
                operation_type = "retrying" if self.is_retry else "processing"
                print(f"{operation_type.capitalize()} {len(self.file_ids)} files")
                total_files = len(self.file_ids)
                
                # Safely get requirement information
                try:
                    original_requirement = Requirement.get_single(self.req_id)
                    self.original_requirement = original_requirement if original_requirement else {}
                except Exception as e:
                    self.log_error(f"Error getting requirement info: {str(e)}")
                    self.original_requirement = {}
                
                for idx, file_id in enumerate(self.file_ids):
                    file_name = self.file_names[idx] if idx < len(self.file_names) else f"File {idx+1}"
                    current_file = idx + 1
                    
                    operation_type = "Retrying" if self.is_retry else "Processing"
                    progress_msg = f"{operation_type} file {current_file} of {total_files}: '{file_name}'"
                    print(file_id)
                    
                    # Send progress update via socket
                    self.add_event('progress_message', {
                        'event_type': 'file_upload',
                        'request_id': self.req_id,
                        'data': {
                            'status': 'processing', 
                            'message': f"{operation_type} file {current_file} of {total_files}: '{file_name}'",
                            'progress': (current_file - 0.5) / total_files * 100,
                            'is_retry': self.is_retry
                        }
                    })
                    
                    try:
                        # Process the document
                        
                        processed, error_message = self.document_seeder_processor.handle_single_document_sync(file_id,file_name,self.project_type)

                        if processed:
                            # Add to successful files list
                            self.successful_files.append({
                                'file_id': file_id,
                                'file_name': file_name
                            })
                        else:
                            # Add to failed files list
                            self.failed_files.append({
                                'file_id': file_id,
                                'file_name': file_name,
                                'error': error_message
                            })

                        # Send progress update via socket after each file (regardless of success/failure)
                        success_count = len(self.successful_files)
                        failed_count = len(self.failed_files)
                        processed_count = success_count + failed_count
                        
                        self.add_event('progress_message', {
                            'event_type': 'file_upload',
                            'request_id': self.req_id,
                            'data': {
                                'status': 'processing', 
                                'message': f"Processed {processed_count} of {total_files} files",
                                'progress': (processed_count / total_files * 100)
                            }
                        })
                    except Exception as e:
                        self.log_error(f"Error processing file {file_name}: {str(e)}")
                        self.add_event('progress_message', {
                            'event_type': 'file_upload',
                            'request_id': self.req_id,
                            'data': {
                                'status': 'error',
                                'message': f"Error processing file {file_name}: {str(e)}",
                                'file_name': file_name,
                                'file_id': file_id,
                                'error': str(e)
                            }
                        })
                
                Requirement.update(self.req_id, status='done', tried=(self.original_requirement.get("tried", 0) + 1))
                # Prepare summary message
                success_count = len(self.successful_files)
                failed_count = len(self.failed_files)
                total_count = success_count + failed_count
                
                if failed_count == 0:
                    if total_count == 1:
                        summary_msg = "Your file was successfully processed and is now ready for use."
                    else:
                        summary_msg = f"All {total_count} files were successfully processed and are now ready for use."
                    summary_status = "success"
                elif success_count == 0:
                    if total_count == 1:
                        summary_msg = "We couldn't process your file. It may be corrupted or in an unsupported format."
                    else:
                        summary_msg = f"We couldn't process any of your {total_count} files. They may be corrupted or in unsupported formats."
                    summary_status = "error"
                else:
                    if failed_count == 1:
                        failure_text = "1 file couldn't be processed"
                    else:
                        failure_text = f"{failed_count} files couldn't be processed"
                    
                    if success_count == 1:
                        success_text = "1 file was successfully processed"
                    else:
                        success_text = f"{success_count} files were successfully processed"
                    
                    summary_msg = f"{success_text} and {failure_text}. The successful files are ready for use."
                    summary_status = "partial"
                
                self.log_info(summary_msg)
                    
                # Notify client that all processing is complete with summary
                # Create a more user-friendly list of failed files if any
                failed_files_messages = []
                if failed_count > 0:
                    for failed_file in self.failed_files:
                        failed_files_messages.append(f"• '{failed_file['file_name']}' - Unable to process")
                

                self.add_event('completed_event', {
                    'event_type': 'file_upload',
                    'request_id': self.req_id,
                    'data': {
                        'status': summary_status,
                        'message': summary_msg,
                        'total_files': total_count,
                        'successful_files': success_count,
                        'failed_files': failed_count,
                        'failed_files_messages': failed_files_messages,
                        'success_details': self.successful_files,
                        'failure_details': self.failed_files
                    }
                })
                    
        except Exception as e:
            self.log_error(f"✗ Error processing files: {str(e)}")
            Requirement.update(self.req_id, status='idle', tried=(self.original_requirement.get("tried", 0) + 1))
            
            # Send error update via socket with user-friendly message
            self.add_event('error_event', {
                'event_type': 'file_upload',
                'request_id': self.req_id,
                'data': {
                    'status': 'error', 
                    'message': "We encountered an unexpected problem while processing your files. Please try again or contact support if the issue persists.",
                    'technical_error': str(e)  # Keep technical error for debugging but don't show to user
                }
            })
    
    def __del__(self):
        """Cleanup method to ensure thread pool is shut down"""
        if hasattr(self, 'thread_pool'):
            self.thread_pool.shutdown(wait=False)
