import gevent
from gevent import monkey
monkey.patch_all()

import uuid
import asyncio
import json
import re
import os
from pydantic import BaseModel, Field
from typing import List, Dict, Any
from models import Chunks, File
from services.pinecone_vector_db import CanopyAI
from services.generate_question_field import QuestionGenerator
from services.synonym_expansion import SynonymGenerator
from services.cohere_embedding import CohereService
from services.evaluation_templates import EvaluationTemplates
from services.fast_apis_service import FastAPIs
from flask import current_app, has_app_context

class ChunkInput(BaseModel):
    id: str
    content: str

class RelevanceRequest(BaseModel):
    chunks: List[ChunkInput]
    question: str
    threshold: float = Field(ge=1.0, le=5.0)

class DataManager:
    def __init__(self):
        self.question_generator = QuestionGenerator()
        self.vec_db = CanopyAI()
        self.synonym_service = SynonymGenerator()
        self.cohere = CohereService()
        self.fast_apis = FastAPIs()

    def split_and_group_sentences(self, text, char_count=1000):

        text = ' '.join(str(text).split())

        grouped_text = []
        overlap_size = char_count // 4
        start = 0

        while start < len(text):
            end = start + char_count

            if end < len(text):
                while end > start and text[end] != ' ':
                    end -= 1
                if end == start:  # If we couldn't find a space, just cut at char_count
                    end = start + char_count

            # Add this chunk to the grouped_text
            grouped_text.append(text[start:end].strip())

            # Move the start point, including overlap
            start = end - overlap_size

        return grouped_text

    def prepare_seed_data(self, section, section_id):
        content_list = self.split_and_group_sentences(section["content"])
        ret = []
        for x in content_list:
            ret.append({
                "content": x,
                "section_id": section_id
            })
        return ret
    
    def detect_section_heading(self, text, preceding_sections):
        """
        This function detects the section heading of a given text.

        Args:
            text (str): The text to detect the section heading of.
            preceding_sections (list): Up to 3 preceding sections of the text.
        """
        prompt_template = EvaluationTemplates.get_template('detect_section_heading')
        prompt_vars = {
            "target_section_content": text,
            "preceding_section_1_content": preceding_sections[-1] if len(preceding_sections) >= 1 else "",
            "preceding_section_2_content": preceding_sections[-2] if len(preceding_sections) >= 2 else "",
            "preceding_section_3_content": preceding_sections[-3] if len(preceding_sections) >= 3 else "",
            "preceding_section_4_content": preceding_sections[-4] if len(preceding_sections) >= 4 else "",
            "preceding_section_5_content": preceding_sections[-5] if len(preceding_sections) >= 5 else "",
        }

        prompt = prompt_template[0]
        prompt = prompt.format(**prompt_vars)
       
        try:
            fast_apis = FastAPIs()
            models = ["llama3-70b-8192", "llama-3.3-70b-versatile", "gemma2-9b-it", "llama-3.1-8b-instant"]
            import random
            model = random.choice(models)  
            llm_response = fast_apis.generate_completion([{"role": "user", "content": prompt}], model=model, max_tokens=20)
            if llm_response and llm_response.strip() != "NO_HEADING_IDENTIFIED":
                return llm_response.strip()
        except Exception as e:
            print(f"Error: {e}")
            return None
        
 
    def seed_data(self, project_id, data, file_id):
        
        seed_data = []

        # Get file information to include document name
        file_info = File.get_by(id=file_id)
        filename = file_info[0]["name"] if file_info and len(file_info) > 0 else ""

        for x in data:
            id = str(uuid.uuid4())
            
            Chunks.create(
                id=id,
                title=x["title"],
                content=x["content"],
                source=filename,  # Use document name as source
                project_id=project_id,
                file_id=file_id,
                summary="",
                page_number=x['page_number']
            )
            # Prepare data for vector database with document name
            chunk_data = self.prepare_seed_data(x, id)
            seed_data.extend(chunk_data)
            

        print("seed data below before send to vec db....")
        self.vec_db.save_to_db(project_id, seed_data)
        return
   
    def seed_data_concurrently(self, project_id, data, file_id,file_name,project_type):
        import gevent
        from gevent.pool import Pool
        
        seed_data = []

        # Get file information to include document name
        file_info = File.get_by(id=file_id)
        filename = file_info[0]["name"] if file_info and len(file_info) > 0 else ""

        for x in data:
            id = str(uuid.uuid4())
            
            Chunks.create(
                id=id,
                title=x["title"],
                content=x["content"],
                source=filename,  # Use document name as source
                project_id=project_id,
                file_id=file_id,
                summary="",
                page_number=x['page_number']
            )
            # Prepare data for vector database with document name
            chunk_data = self.prepare_seed_data(x, id)
            seed_data.extend(chunk_data)
        
        print("Seed data below before send to vec db....")
        self.vec_db.save_to_db(project_id, seed_data,file_name,project_type)
        return

    async def get_data_async(self, project_id, query, limit=5):
        start_time = asyncio.get_event_loop().time()

        k = 100
        job = asyncio.to_thread(self.vec_db.get_data, project_id, [query], k)
        ret = await job
        result = []
        length = len(ret["ids"][0])
        for x in range(length):
            result.append({
                "id": ret["ids"][0][x],
                "matched_content": ret["metadatas"][0][x]["documents"],
                "text": ret["metadatas"][0][x]["documents"],
                "section_id": ret["metadatas"][0][x]["section_id"],
                "score": ret["distances"][0][x]
            })

        end_time = asyncio.get_event_loop().time()
        elapsed_time = end_time - start_time
        print(f"get_data_async took {elapsed_time:.2f} seconds for query: {query}...")
        reranked_results = self.cohere.rerank_texts(query, result, limit)
        # print('reranked results: ', reranked_results)

        return reranked_results

    def get_data_sync(self, project_id, query, limit=5):
        k = 100
        ret = self.vec_db.get_data(project_id, [query], k)
        # print('ret: ', ret)
        result = []
        length = len(ret["ids"][0])
        for x in range(length):
            result.append({
                "id": ret["ids"][0][x],
                "matched_content": ret["metadatas"][0][x]["documents"],
                "text": ret["metadatas"][0][x]["documents"],
                "section_id": ret["metadatas"][0][x]["section_id"],
                "title": ret["metadatas"][0][x].get("title", ""),
                "score": ret["distances"][0][x]
            })

        reranked_results = self.cohere.rerank_texts(query, result, limit)
        return reranked_results


    async def get_multiple_query_data_async(self, project_id, queries, limit=5):
        print('this is query: ', queries)
        tasks = [self.get_data_async(project_id, x, limit) for x in queries]
        result = await asyncio.gather(*tasks)
        return result

    async def get_multiple_query_data_async_v2(self, project_id, query, limit=5):
        print('this is query: ', query)
        result = await self.get_data_async(project_id, query, limit)
        return result

    async def extract_data(self, project_id, texts, page_limit=10):
        try:
            print("Extracting data...")
            # generate similar questions to improve search
            data = await self.question_generator._generate_questions_claude(texts)
            tasks = []
            print("Question generated successfully...")
            # print(data)
            for x in data:
                tasks.append(self.get_multiple_query_data_async(project_id, x, 5))
            results = await asyncio.gather(*tasks)
            print('multiple query')
            # print(results)

            # sys.exit()
            sz = len(texts)
            final_results = [{"source_map": {}, "source_list": [], "questions": data[x]} for x in range(sz)]
            for i in range(sz):
                x = results[i]
                source_map = final_results[i]["source_map"]
                source_list = final_results[i]["source_list"]
                for y in x:
                    print('y in z')
                    for z in y:
                        if z["section_id"] not in source_map:
                            source_map[z["section_id"]] = z
                            source_list.append(z)
                        if source_map[z["section_id"]]["score"] <= z["score"]:
                            source_map[z["section_id"]]["matched_content"] = z["matched_content"]

                        source_map[z["section_id"]]["score"] = max(z["score"], source_map[z["section_id"]]["score"])

                for p in source_list:
                    chunk = Chunks.get_by(id=p["section_id"])
                    print('p in source_list')
                    title = ""
                    content = ""
                    if len(chunk) != 0:
                        title = chunk[0]["title"]
                        content = chunk[0]["content"]
                        file_info = File.get_by(id=chunk[0]["file_id"])

                        if not file_info:
                            print(f"Skipping section_id={p['section_id']} due to missing file information.")
                            continue

                        file_name = file_info[0]["name"]
                        p["source"] = file_name
                        p["title"] = title

                        p["page_number"] = chunk[0].get("page_number", None)

                        if len(content) < 5:
                            p["content"] = p['matched_content']
                        else:
                            p["content"] = content
                    else:

                        p["content"] = p['matched_content']
                        p["page_number"] = None

            for x in final_results:
                print('x in final_results')
                x["source_list"] = sorted(x["source_list"], key=lambda x: x["score"], reverse=True)[0:page_limit]

            return final_results

        except Exception as e:
            print(e)

    async def extract_data_v2(self, project_id, Question, subQuestions=[], page_limit=10):
        try:
            # print(f"Extracting synonym data with len: {len(subQuestions)}")
            question_and_synonyms = " ".join([Question[0]] + subQuestions)
            # Concurrently fetch data for all questions
            tasks = [self.get_data_async(project_id, question_and_synonyms, page_limit)]

            if len(subQuestions) > 0:
                tasks.extend(self.get_data_async(project_id, question, page_limit) for question in subQuestions)

            results = await asyncio.gather(*tasks, return_exceptions=True)

            # print("Multiple queries completed and length is: ", len(results[0]))

            # Prepare results structure
            final_results = [{"source_map": {}, "source_list": [], "questions": Question}]
            if len(subQuestions) > 0:
                final_results.extend({"source_map": {}, "source_list": [], "questions": [subQuestions[idx]]} for idx in range(len(subQuestions)))

            # print('length of final results: ', len(final_results))

            # Process each result
            for i, task_result in enumerate(results):
                if isinstance(task_result, Exception):
                    print(f"Error in task {i}: {task_result}")
                    continue  # Skip processing if an exception occurred in this task

                source_map = final_results[i]["source_map"]
                source_list = final_results[i]["source_list"]

                # print('checkpoint QMP 197')
                # print('this is task result: ', task_result)

                # Populate `source_map` and `source_list`
                for section in task_result:
                    section_id = section["section_id"]

                    # Check if section already exists in the map
                    if section_id not in source_map:
                        source_map[section_id] = section
                        source_list.append(section)

                    # Update the matched content and score if the new one is better
                    if source_map[section_id]["score"] <= section["score"]:
                        source_map[section_id]["matched_content"] = section["matched_content"]
                    source_map[section_id]["score"] = max(section["score"], source_map[section_id]["score"])

                # print('checkpoint QMP 214....')
                # Enrich source list with chunk and file information
                for source in source_list:
                    chunk = Chunks.get_by(id=source["section_id"])
                    if not chunk:
                        # print(f"Skipping section_id={source['section_id']} due to missing chunk data.")
                        source["content"] = source["matched_content"]
                        source["page_number"] = None
                        continue

                    chunk_data = chunk[0]
                    file_info = File.get_by(id=chunk_data["file_id"])
                    if not file_info:
                        # print(f"Skipping section_id={source['section_id']} due to missing file information.")
                        source["content"] = source["matched_content"]
                        source["page_number"] = None
                        continue

                    # Update source information
                    source["source"] = file_info[0]["name"]
                    source["title"] = chunk_data["title"]
                    source["page_number"] = chunk_data.get("page_number")
                    source["content"] = chunk_data["content"] if len(chunk_data["content"]) >= 5 else source["matched_content"]

                # print('checkpoint QMP 238.....')
                # Sort source_list by score and limit to `page_limit`
                # source_list.sort(key=lambda x: x["score"], reverse=True)
                final_results[i]["source_list"] = source_list[:page_limit]

            # print("Data extraction complete.", final_results)
            return final_results

        except Exception as e:
            print(f"An error occurred during data extraction: {e}")
            return []


    def extract_data_v2_sync(self, project_id, Question, subQuestions=[], page_limit=10):
        try:
            question_and_synonyms = " ".join([Question[0]] + subQuestions)

            # Create list of tasks
            tasks = [gevent.spawn(self.get_data_sync, project_id, question_and_synonyms, page_limit)]

            if len(subQuestions) > 0:
                for question in subQuestions:
                    tasks.append(gevent.spawn(self.get_data_sync, project_id, question, page_limit))

            # Wait for all tasks to complete
            gevent.joinall(tasks)
            results = [task.value for task in tasks]

            # Prepare results structure
            final_results = [{"source_map": {}, "source_list": [], "questions": Question}]
            if len(subQuestions) > 0:
                final_results.extend({"source_map": {}, "source_list": [], "questions": [subQuestions[idx]]} for idx in range(len(subQuestions)))

            # Process each result
            for i, task_result in enumerate(results):
                if task_result is None:  # Handle failed tasks
                    continue

                source_map = final_results[i]["source_map"]
                source_list = final_results[i]["source_list"]

                # Populate `source_map` and `source_list`
                for section in task_result:
                    section_id = section["section_id"]

                    if section_id not in source_map:
                        source_map[section_id] = section
                        source_list.append(section)

                    if source_map[section_id]["score"] <= section["score"]:
                        source_map[section_id]["matched_content"] = section["matched_content"]
                    source_map[section_id]["score"] = max(section["score"], source_map[section_id]["score"])

                # Enrich source list with chunk and file information
                for source in source_list:
                    chunk = Chunks.get_by(id=source["section_id"])
                    if not chunk:
                        source["content"] = source["matched_content"]
                        source["page_number"] = None
                        continue

                    chunk_data = chunk[0]
                    file_info = File.get_by(id=chunk_data["file_id"])
                    if not file_info:
                        source["content"] = source["matched_content"]
                        source["page_number"] = None
                        continue

                    source["source"] = file_info[0]["name"]
                    if not source.get("title"):
                        source["title"] = chunk_data["title"]
                    source["page_number"] = chunk_data.get("page_number")
                    source["content"] = chunk_data["content"] if len(chunk_data["content"]) >= 5 else source["matched_content"]

                final_results[i]["source_list"] = source_list[:page_limit]
            print('final results (after relevance filtering): ', len(final_results[0]["source_list"]))
            return final_results

        except Exception as e:
            print(f"An error occurred during data extraction: {e}")
            return [{"source_map": {}, "source_list": [], "questions": Question}]
    

    async def extract_data_chronobid(self, project_id, criteria):
        print("Extracting pinecone data for chronobid main...")
        satisfying_data = {"ids": [], "metadatas": [], "questions": [], "content":[]}
        print('right here...')
        concatenated_criteria = f"{criteria['description']} {criteria['name']}"
        print('stopped here....')
       # Generate synonyms
        synonyms = await self.synonym_service.generate_synonym(
            f"{criteria['name']}\n Description: {criteria['description']}"
        )
        print('synonym generated successfully...')
        print('this is synonyms: ', synonyms)

        # Parse synonyms with error handling
        name_descriptions = re.findall(
            r"Name:\s*(.+?)\n\s*Description:\s*(.+?)(?:\n|</SYNONYMS>)",
            synonyms,
            re.DOTALL
        )
        print('name & desc: ', name_descriptions)

        # Create name-description pairs
        name_description_pairs = [
            f"{name.strip()} - {description.strip()}"
            for name, description in name_descriptions
        ]

        print('name & desc pairs: ', name_description_pairs)

        # Query Pinecone
        print(f"PROJECT ID HERE : {project_id}")
        results = self.vec_db.get_data(project_id, [concatenated_criteria], 3)
        results2 = self.vec_db.get_data(project_id, [name_description_pairs[0]], 2)
        results3 = self.vec_db.get_data(project_id, [name_description_pairs[1]], 3)
        results4 = self.vec_db.get_data(project_id, [name_description_pairs[2]], 2)
        # results5 = self.vec_db.get_data(project_id, [name_description_pairs[3]], 3)
        # results6 = self.vec_db.get_data(project_id, [name_description_pairs[4]], 2)

        total_result = {**results, **results2, **results3, **results4}  # Use unpacking to merge dictionaries

        print('total results: ', total_result)

        # Extract data that satisfies the criteria
        if not any(total_result["ids"]) and not any(total_result["metadatas"]):
            print('empty response from pinecone...')
            return satisfying_data

        for ids, metadatas in zip(total_result["ids"], total_result["metadatas"]):
            satisfying_data["ids"].append(ids)
            for metadata in metadatas:
                # print('this is metadata: ', metadata)
                chunk = Chunks.get_by(id=metadata["section_id"])
                if len(chunk) > 0:
                    metadata['detailed_chunk'] = chunk[0]["content"]
                    metadata['title'] = chunk[0]["title"]
                    metadata['page_number'] = chunk[0]["page_number"]
                    metadata['source'] = chunk[0]["source"]
                satisfying_data["metadatas"].append(metadata)

        # print("Satisfying Pinecone data:", satisfying_data)
        return satisfying_data

    def extract_data_chronobid_sync(self, project_id, criteria):
        print("Extracting pinecone data for chronobid main...")
        satisfying_data = {"ids": [], "metadatas": [], "questions": [], "content":[]}
        print('right here...')
        concatenated_criteria = f"{criteria['description']} {criteria['name']}"
        print('stopped here....')
       # Generate synonyms
        synonyms = self.synonym_service.generate_synonym_sync(
            f"{criteria['name']}\n Description: {criteria['description']}"
        )
        print('synonym generated successfully...')
        # print('this is synonyms: ', synonyms)

        # Parse synonyms with error handling
        name_descriptions = re.findall(
            r"Name:\s*(.+?)\n\s*Description:\s*(.+?)(?:\n|</SYNONYMS>)",
            synonyms,
            re.DOTALL
        )
        # print('name & desc: ', name_descriptions)

        # Create name-description pairs
        name_description_pairs = [
            f"{name.strip()} - {description.strip()}"
            for name, description in name_descriptions
        ]

        # print('name & desc pairs: ', name_description_pairs)

        # Query Pinecone
        print(f"PROJECT ID HERE : {project_id}")
        results = self.vec_db.get_data(project_id, [concatenated_criteria], 3)
        results2 = self.vec_db.get_data(project_id, [name_description_pairs[0]], 2)
        results3 = self.vec_db.get_data(project_id, [name_description_pairs[1]], 3)
        results4 = self.vec_db.get_data(project_id, [name_description_pairs[2]], 2)
        # results5 = self.vec_db.get_data(project_id, [name_description_pairs[3]], 3)
        # results6 = self.vec_db.get_data(project_id, [name_description_pairs[4]], 2)

        total_result = {**results, **results2, **results3, **results4}  # Use unpacking to merge dictionaries

        # print('total results: ', total_result)

        # Extract data that satisfies the criteria
        if not any(total_result["ids"]) and not any(total_result["metadatas"]):
            print('empty response from pinecone...')
            return satisfying_data

        for ids, metadatas in zip(total_result["ids"], total_result["metadatas"]):
            satisfying_data["ids"].append(ids)
            for metadata in metadatas:
                # print('this is metadata: ', metadata)
                chunk = Chunks.get_by(id=metadata["section_id"])
                if len(chunk) > 0:
                    metadata['detailed_chunk'] = chunk[0]["content"]
                    metadata['title'] = chunk[0]["title"]
                    metadata['source'] = chunk[0]["source"]
                    metadata['page_number'] = chunk[0]["page_number"]
                satisfying_data["metadatas"].append(metadata)

        # print("Satisfying Pinecone data:", satisfying_data)
        return satisfying_data



    async def extract_uploaded_data_chronobid(self, project_id, criteria):
        print("Extracting pinecone data for chronobid...")

        concatenated_criteria = f"{criteria['description']} {criteria['name']}"
        synonyms = await self.synonym_service.generate_synonym(f"{criteria['name']}\n Description: {criteria['description']}")
        print('uploaded doc synonym generated successfully...')

        # Capture both name and description together
        name_descriptions = re.findall(r"Name:\s*(.+?)\n\s*Description:\s*(.+?)(?:\n|</SYNONYMS>)", synonyms, re.DOTALL)

        # Combine each name and description into a single string
        name_description_pairs = [f"{name.strip()} - {description.strip()}" for name, description in name_descriptions]

        # Query Pinecone
        print('project id: ', f"temp_{project_id}")
        print('concat croteria: ', concatenated_criteria)
        results = self.vec_db.get_uploaded_data(f"temp_{project_id}", [concatenated_criteria], 3)
        results2 = self.vec_db.get_uploaded_data(f"temp_{project_id}", [name_description_pairs[0]], 2)
        results3 = self.vec_db.get_uploaded_data(f"temp_{project_id}", [name_description_pairs[1]], 3)
        results4 = self.vec_db.get_uploaded_data(f"temp_{project_id}", [name_description_pairs[2]], 2)
        # results5 = self.vec_db.get_uploaded_data(f"temp_{project_id}", [name_description_pairs[3]], 3)
        # results6 = self.vec_db.get_uploaded_data(f"temp_{project_id}", [name_description_pairs[4]], 2)

        total_result = {**results, **results2, **results3, **results4}  # Use unpacking to merge dictionaries

        print(total_result)
        # Extract data that satisfies the criteria
        if not any(total_result["ids"]) and not any(total_result["metadatas"]):
            print('empty response from pinecone for dataset2...')
            return total_result

        # print("Satisfying Pinecone data:", satisfying_data)
        return total_result

    async def extract_data_specs_comply(self, project_id, criteria):
        print("Extracting pinecone data for specs comply...")
        satisfying_data = {"ids": [], "metadatas": [], "questions": []}

        concatenated_criteria = f"{criteria} \nDescription: {criteria}"

        synonyms = await self.synonym_service.generate_synonym(f"{criteria}\n Description: {criteria}")

        # Capture both name and description together
        name_descriptions = re.findall(r"Name:\s*(.+?)\n\s*Description:\s*(.+?)(?:\n|</SYNONYMS>)", synonyms, re.DOTALL)
        name_description_pairs = [f"{name.strip()} - {description.strip()}" for name, description in name_descriptions]


        # Query Pinecone
        results = self.vec_db.get_data(project_id, [concatenated_criteria],2)
        results2 = self.vec_db.get_data(project_id, [name_description_pairs[0]], 2)
        results3 = self.vec_db.get_data(project_id, [name_description_pairs[1]], 3)
        results4 = self.vec_db.get_data(project_id, [name_description_pairs[2]], 2)
        # results5 = self.vec_db.get_data(project_id, [name_description_pairs[3]], 3)
        # results6 = self.vec_db.get_data(project_id, [name_description_pairs[4]], 2)

        total_result = {**results, **results2, **results3, **results4}  # Use unpacking to merge dictionaries


        if not any(total_result["ids"]) and not any(total_result["metadatas"]):
            print('empty response from pinecone...')
            return satisfying_data

        # Extract data that satisfies the criteria
        for ids, metadatas in zip(total_result["ids"], total_result["metadatas"]):
            satisfying_data["ids"].append(ids)
            for metadata in metadatas:
                # print('this is metadata: ', metadata)
                chunk = Chunks.get_by(id=metadata["section_id"])
                if len(chunk) > 0:
                    metadata['detailed_chunk'] = chunk[0]["content"]
                    metadata['title'] = chunk[0]["title"]
                    metadata['page_number'] = chunk[0]["page_number"]
                satisfying_data["metadatas"].append(metadata)
        print("done with searching pinecone....")

        return satisfying_data

    def extract_data_specs_comply_v2(self, project_id, criteria):
        print("Extracting pinecone data for specs comply...")
        satisfying_data = {"ids": [], "metadatas": [], "questions": []}

        concatenated_criteria = f"{criteria} \nDescription: {criteria}"

        synonyms = self.synonym_service.generate_synonym_sync(f"{criteria}\n Description: {criteria}")

        # Capture both name and description together
        name_descriptions = re.findall(r"Name:\s*(.+?)\n\s*Description:\s*(.+?)(?:\n|</SYNONYMS>)", synonyms, re.DOTALL)
        name_description_pairs = [f"{name.strip()} - {description.strip()}" for name, description in name_descriptions]


        # Query Pinecone
        results = self.vec_db.get_data(project_id, [concatenated_criteria],2)
        results2 = self.vec_db.get_data(project_id, [name_description_pairs[0]], 2)
        results3 = self.vec_db.get_data(project_id, [name_description_pairs[1]], 3)
        results4 = self.vec_db.get_data(project_id, [name_description_pairs[2]], 2)
        # results5 = self.vec_db.get_data(project_id, [name_description_pairs[3]], 3)
        # results6 = self.vec_db.get_data(project_id, [name_description_pairs[4]], 2)

        total_result = {**results, **results2, **results3, **results4}  # Use unpacking to merge dictionaries


        if not any(total_result["ids"]) and not any(total_result["metadatas"]):
            print('empty response from pinecone...')
            return satisfying_data

        # Extract data that satisfies the criteria
        for ids, metadatas in zip(total_result["ids"], total_result["metadatas"]):
            satisfying_data["ids"].append(ids)
            for metadata in metadatas:
                # print('this is metadata: ', metadata)
                chunk = Chunks.get_by(id=metadata["section_id"])
                if len(chunk) > 0:
                    metadata['detailed_chunk'] = chunk[0]["content"]
                    metadata['title'] = chunk[0]["title"]
                    metadata['page_number'] = chunk[0]["page_number"]
                satisfying_data["metadatas"].append(metadata)
        print("done with searching pinecone....")

        return satisfying_data



    def get_relevant_chunks(self, chunks, question, threshold=4.0):
        """Score and filter chunks based on relevance with few-shot examples."""
        relevant_chunks = []
        
        few_shot = """<EXAMPLES>
        Question: What is the maximum pressure rating?
        ID 1: The system operates at 150 psi maximum working pressure.
        ID 2: Regular maintenance should be performed monthly.
        ID 3: All pressure vessels must be certified and tested.
        ID 4: Maximum design temperature is 200°F.
        ID 5: Operating procedures must be followed.
        ID 6: Sigmund Frued is a neurologist.
        Expected output: 5,1,3,2,1,1
        
        Question: What are the maintenance requirements?
        ID 1: Weekly inspections of seals required.
        ID 2: Paint color should be safety yellow.
        ID 3: Maintenance schedule: monthly service required.
        ID 4: System warranty is 12 months.
        ID 5: Clean filters every 2 weeks.
        Expected output: 4,1,5,2,4
        </EXAMPLES>
        ---
        
        """
        
        rules = """
        Rate each text on a scale of 1-5 based on relevance to the question.
        5 being the most relevant and 1 being the least relevant.
        
        Output only comma-separated number matching the example format above.
        DO NOT Explain your answer.
        ONLY RETURN THE COMMA-SEPARATED NUMBERS CORRESPONDING TO THE ORDER OF THE TEXTS.
        """
        
        
        # Process chunks in batches of 5
        for i in range(0, len(chunks), 5):
            batch = chunks[i:i+5]
            batch_prompt = f"""
            Question: {question}
            {' '.join([f'ID {j+1}: {chunk["content"]}' for j, chunk in enumerate(batch)])}
            
            {few_shot}
            
            {rules}
            I EXPECT ONLY {len(batch)} NUMBERS IN YOUR RESPONSE.
            """
            try:
                print('this is batch prompt: ', batch_prompt)
                response = self.fast_apis.generate_completion(
                    [{"role": "user", "content": batch_prompt}],
                    model="llama-3.1-8b-instant",
                    max_tokens=50
                )
                
                
                # Parse scores, default to including chunk if parsing fails
                try:
                    scores = [float(s.strip()) for s in response.strip().split(',')]
                    print('this is scores: ', scores)
                    if len(scores) != len(batch):
                        raise ValueError("Score count mismatch")
                except Exception as e:
                    # Fail-safe: Include all chunks if parsing fails
                    scores = [5.0] * len(batch)
                
                for chunk, score in zip(batch, scores):
                    if score >= threshold:
                        # Preserve all fields from the original chunk and add relevance score
                        filtered_chunk = chunk.copy()
                        filtered_chunk["relevance_score"] = score
                        relevant_chunks.append(filtered_chunk)
            except Exception as e:
                # LLM call failed, include all chunks from this batch
                print(f"Error in LLM call: {e}")
                # Preserve all fields in error case too
                relevant_chunks.extend([
                    {**chunk, "relevance_score": 5.0}  # Merge all original fields with relevance score
                    for chunk in batch
                ])
        
        return sorted(relevant_chunks, key=lambda x: x["relevance_score"], reverse=True)
    
    def extract_standard_and_project(self, page_limit=10):
        try:
            question_and_synonyms = " ".join([Question[0]] + subQuestions)

            # Create list of tasks
            tasks = [gevent.spawn(self.get_data_sync, project_id, question_and_synonyms, page_limit)]

            if len(subQuestions) > 0:
                for question in subQuestions:
                    tasks.append(gevent.spawn(self.get_data_sync, project_id, question, page_limit))

            # Wait for all tasks to complete
            gevent.joinall(tasks)
            results = [task.value for task in tasks]

            # Prepare results structure
            final_results = [{"source_map": {}, "source_list": [], "questions": Question}]
            if len(subQuestions) > 0:
                final_results.extend({"source_map": {}, "source_list": [], "questions": [subQuestions[idx]]} for idx in range(len(subQuestions)))

            # Process each result
            for i, task_result in enumerate(results):
                if task_result is None:  # Handle failed tasks
                    continue

                source_map = final_results[i]["source_map"]
                source_list = final_results[i]["source_list"]

                # Populate `source_map` and `source_list`
                for section in task_result:
                    section_id = section["section_id"]

                    if section_id not in source_map:
                        source_map[section_id] = section
                        source_list.append(section)

                    if source_map[section_id]["score"] <= section["score"]:
                        source_map[section_id]["matched_content"] = section["matched_content"]
                    source_map[section_id]["score"] = max(section["score"], source_map[section_id]["score"])

                # Enrich source list with chunk and file information
                for source in source_list:
                    chunk = Chunks.get_by(id=source["section_id"])
                    if not chunk:
                        source["content"] = source["matched_content"]
                        source["page_number"] = None
                        continue

                    chunk_data = chunk[0]
                    file_info = File.get_by(id=chunk_data["file_id"])
                    if not file_info:
                        source["content"] = source["matched_content"]
                        source["page_number"] = None
                        continue

                    source["source"] = file_info[0]["name"]
                    if not source.get("title"):
                        source["title"] = chunk_data["title"]
                    source["page_number"] = chunk_data.get("page_number")
                    source["content"] = chunk_data["content"] if len(chunk_data["content"]) >= 5 else source["matched_content"]

                final_results[i]["source_list"] = source_list[:page_limit]
            print('final results (after relevance filtering): ', len(final_results[0]["source_list"]))
            return final_results

        except Exception as e:
            print(f"An error occurred during data extraction: {e}")
            return [{"source_map": {}, "source_list": [], "questions": Question}]

if __name__ == "__main__":
    data_manager = DataManager()
    
    # Test data
    test_chunks = [
        {"id": "1", "content": "Oil drilling safety procedures require proper equipment inspection before operation."},
        {"id": "2", "content": "The reservoir pressure was measured at 5000 psi during the test."},
        {"id": "3", "content": "All personnel must wear appropriate PPE when on the drilling floor."},
        {"id": "4", "content": "The geological formation consists mainly of sandstone."}
    ]
    
    test_question = "What are geological formations in the reservoir?"
    threshold = 3.0
    
    # Get relevant chunks
    relevant_results = data_manager.get_relevant_chunks(test_chunks, test_question, threshold)
    
    print("\nTest Question:", test_question)
    print("\nRelevant Chunks:")
    for chunk in relevant_results:
        print(f"\nID: {chunk['id']}")
        print(f"Content: {chunk['content']}")