import gevent
from gevent import monkey
monkey.patch_all()
import os, re
import json
import asyncio
from models import Requirement, Project,BidFile,EquipmentPackage
from services.reasoning_chronobid import ChronobidReasoning
from services.data_handler import DataManager
from services.report_manager import ReportManager
from services.process_cbp_dataset_1 import Dataset1Extractor
from services.process_cbp_dataset_2 import Dataset2Extractor
from services.update_backend_status import BackendStatusUpdater
from cbp.pipelines.technical_criteria_processor import TechnicalCriteriaEvaluator
from cbp.pipelines.bid_evaluation.comparison_table import ComparisonTableCreator,generate_comparison_summary
from cbp.pipelines.bid_evaluation.criteria_formula import CriteriaFormulaProcessor
from cbp.pipelines.bid_evaluation.report.utils import extract_criteria_and_reasons
from services.faiss_embedding import FaissEmbedding
from flask_socketio import emit, join_room
import time
from flask import request
import redis

class ChronoMultibidDocumentProcessor:
    def __init__(self, socket_manager, is_test=False):
        self.is_test = is_test
        self.socket_manager = socket_manager
        self.parent_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
        self.env_data = self._load_env()
        self.chronobid_reasoning = ChronobidReasoning()
        self.data_manager = DataManager()
        self.dataset2_extractor = Dataset2Extractor(self.socket_manager)
        self.report_manager = None
        self.backend_status_updater = BackendStatusUpdater()
        # Add a global tracker for processed criteria per bid
        self.processed_criteria_tracker = {}
        self.technical_criteria_names =  []
        self.faiss_processor = FaissEmbedding()

        self.data_aggregrated_report = {} #contains useful data during processign that will be later used for final report
        self.technical_criteria_bid_counter = 0


        self.formula_criteria_weights = {}

        self.tender_relevant_chunks = {} #store relevant chunks from tender documents for each criteria
        
    def get_current_socketio(self):
        """Fetch the current socketio instance."""
        return self.get_socketio_instance()

    def _load_env(self):
        env_path = os.path.join(self.parent_dir, 'env.json')
        with open(env_path, 'r') as f:
            return json.load(f)
        
        
    async def process_cbp(self, requirement_id):
        print(f"Processing CBP for requirement_id: {requirement_id}")  # Debugging statement
        req = Requirement.get_single(requirement_id)
        print(f"Retrieved requirement: {req}")  # Debugging statement
        bid_info = json.loads(req["multi_bids_info"])
        print(f"Parsed bid_info: {bid_info}")  # Debugging statement
        
        # Store bid information for reference during processing
        self.multi_bid_info = bid_info  # Store the complete bid_info dictionary
        
        # Simply await the handle_multiple_bids method directly
        return await self.handle_multiple_bids(requirement_id)
     
    def process_cbp_sync(self, requirement_id):
        from init import app
        
        def _run_async():
            try:
                # Ensure each greenlet has an application context
                with app.app_context():
                    # Reset the criteria tracker for a new processing run
                    self.processed_criteria_tracker = {}
                    
                    print(f"Processing CBP for requirement_id: {requirement_id}")
                    req = Requirement.get_single(requirement_id)
                    print(f"Retrieved requirement: {req}")
                    bid_info = json.loads(req["multi_bids_info"])
                    print(f"Parsed bid_info: {bid_info}")
                    
                    # Store bid information for reference during processing
                    self.multi_bid_info = bid_info  # Store the complete bid_info dictionary
                    
                    # Simply await the handle_multiple_bids method directly
                    return self.handle_multiple_bids_sync(requirement_id)
            except Exception as e:
                print(f"Error in process_query greenlet: {e}")
                raise
        
        # Spawn and return the greenlet
        return gevent.spawn(_run_async)

    async def handle_multiple_bids(self, requirement_id):
        try:
            # Get the original requirement
            original_requirement = Requirement.get_single(requirement_id)
            
            # Check if requirement is already done or has been tried too many times
            if original_requirement['status'] == 'done':
                print(f"CBP Requirement with id: {requirement_id} is already done. Exiting...")
                return None
                
            if original_requirement.get('tried', 0) > 1:
                print(f"CBP Requirement with id: {requirement_id} has been tried more than once. Exiting...")
                return None
            
            # Use bid_info directly instead of loading from requirement
            bids = self.multi_bid_info
            print(f"Bids are : {bids}")
            
            if not bids or len(bids) == 0:
                # If no bids, just process the single requirement as before
                return await self.handle_single_new_document_v2(requirement_id)
            
            # Notify the client that we're starting to process multiple bids
            self.add_event(requirement_id, 'progress_message', {
                'event_type': 'CBP',
                'request_id': requirement_id,
                'data': {'status': 'extracting_sources', 'message': f"Welcome to Chronobid Assistant... Processing {len(bids)} bids sequentially"}
            })
            
            # Initialize a list to store all logs
            full_log = []
            
            # Process each bid sequentially using bid_index to ensure order
            processed_bid_ids = set()  # Keep track of processed bid IDs
            
            for bid_info in sorted(bids, key=lambda x: x.get('bid_index', 0)):
                bid_id = bid_info.get('id')
                
                # Skip if we've already processed this bid ID
                if bid_id in processed_bid_ids:
                    print(f"Skipping duplicate bid ID: {bid_id}")
                    continue
                    
                processed_bid_ids.add(bid_id)
                
                # Duplicate the requirement for this bid
                dup_requirement = original_requirement.copy()
                bid_requirement_id = f"{requirement_id}___{bid_id}"
                dup_requirement['id'] = bid_requirement_id
                dup_requirement['original_id'] = requirement_id
                dup_requirement['bid_data'] = bid_info
                dup_requirement['bid_index'] = bid_info.get('bid_index', len(processed_bid_ids) - 1)
                dup_requirement['bidder_name'] = bid_info.get('bidder_name', f'Bidder {len(processed_bid_ids)}')
                
                # Notify about processing current bid
                self.add_event(requirement_id, 'progress_message', {
                    'event_type': 'CBP',
                    'request_id': requirement_id,
                    'data': {'status': 'processing_bid', 'message': f"Processing bid from {dup_requirement['bidder_name']}..."}
                })
                
                try:
                    # Process single bid
                    result = await self.handle_single_new_document_v2(bid_requirement_id)
                    
                    if result:  # If the bid processing was successful
                        # Add bid-specific info to the log
                        result['bid_index'] = bid_info.get('bid_index')
                        result['bid_requirement_id'] = bid_requirement_id
                        result['bidder_name'] = bid_info.get('bidder_name')
                        result['bid_id'] = bid_id
                        result['status'] = 'completed'
                        
                        full_log.append(result)

                
                    else:
                        full_log.append({
                            "bid_index": bid_info.get('bid_index'),
                            "bid_requirement_id": bid_requirement_id,
                            "bid_id": bid_id,
                            "bidder_name": bid_info.get('bidder_name'),
                            "error": "Processing failed",
                            "status": "failed"
                        })

                except Exception as e:
                    print(f"Error processing bid {bid_id}: {e}")
                    full_log.append({
                        "bid_index": bid_info.get('bid_index'),
                        "bid_requirement_id": bid_requirement_id,
                        "bid_id": bid_id,
                        "bidder_name": bid_info.get('bidder_name'),
                        "error": str(e),
                        "status": "failed"
                    })
            
            # Update the original requirement with the full log
            Requirement.update(requirement_id, status='done', full_log=json.dumps(full_log))
            
            # Create an aggregated report for all bids
            aggregated_report_manager = ReportManager(
                self.env_data.get('CHRONOBID_DIR'),
                original_requirement["project_id"],
                requirement_id
            )
            
            # Get all individual bid reports
            all_bid_reports = aggregated_report_manager.get_all_bid_reports()
            
            # Create aggregated report
            aggregated_log = {
                "requirement_id": requirement_id,
                "project_id": original_requirement["project_id"],
                "total_bids": len(bids),
                "bid_reports": all_bid_reports,
                "full_log": full_log,
                "aggregated_at": time.strftime("%Y-%m-%d %H:%M:%S")
            }
            
            # Save aggregated report
            aggregated_report_manager.create_initial_report(aggregated_log)
            
            # Emit completion event with full results
            self.add_event(requirement_id, 'completed_event', {
                'event_type': 'CBP',
                'request_id': requirement_id,
                'data': {
                    'full_log': full_log,
                    'bid_count': len(bids)
                }
            })
            
            
            await self._finalize_log_and_status(full_log)
            return full_log
            
        except Exception as e:
            print(f"Error in handle_multiple_bids: {e}")
            Requirement.update(requirement_id, status='idle', tried=(original_requirement.get("tried", 0) + 1))
            return None
    

    def handle_multiple_bids_sync(self, requirement_id):
        try:
            # Get the original requirement
            original_requirement = Requirement.get_single(requirement_id)

            if original_requirement['status'] == 'done':
                print(f"CBP Requirement with id: {requirement_id} is already done. Exiting...")
                return None
            
            # Reset the technical criteria bid counter for new processing
            self.technical_criteria_bid_counter = 0
            
            # Use bid_info directly instead of loading from requirement
            bids = self.multi_bid_info
            print(f"Bids are : {bids}")
            
            if not bids or len(bids) == 0:
                # If no bids, just process the single requirement as before
                return self.handle_single_new_document_v2_sync(requirement_id)
            
            # Notify the client that we're starting to process multiple bids
            self.add_event(requirement_id, 'progress_message', {
                'event_type': 'CBP',
                'request_id': requirement_id,
                'data': {'status': 'extracting_sources', 'message': f"Welcome to Chronobid Assistant... Processing {len(bids)} bids concurrently"}
            })
            
            # Initialize a list to store all logs
            full_log = []
            
            # Create a list to store all greenlets
            greenlets = []
            
            # Process each bid concurrently using gevent
            for bid_info in sorted(bids, key=lambda x: x.get('bid_index', 0)):
                bid_id = bid_info.get('id')
                # Process each bid synchronously
                result = self._process_single_bid(
                    original_requirement,
                    requirement_id,
                    bid_info,
                    bid_id
                )
                if result:
                    full_log.append(result)
            
            # Wait for all greenlets to complete
            # gevent.joinall(greenlets)
            
            # # Collect results from all greenlets
            # for greenlet in greenlets:
            #     result = greenlet.value
            #     if result:
            #         full_log.append(result)
            
            # Update the original requirement with the full log
            Requirement.update(requirement_id, status='done')
            
            # Create an aggregated report for all bids
            aggregated_report_manager = ReportManager(
                self.env_data.get('CHRONOBID_DIR'),
                original_requirement["project_id"],
                requirement_id
            )
            

            # Get all individual bid reports
            all_bid_reports = aggregated_report_manager.get_all_bid_reports()
            
            # Create aggregated report
            aggregated_log = {
                "requirement_id": requirement_id,
                "project_id": original_requirement["project_id"],
                "total_bids": len(bids),
                "bid_reports": all_bid_reports,
                "full_log": full_log,
                "aggregated_at": time.strftime("%Y-%m-%d %H:%M:%S")
            }
            
            # Save aggregated report
            aggregated_report_manager.create_initial_report(aggregated_log)
            
            try:

                self.add_event(requirement_id, 'progress_message', {
                    'event_type': 'CBP',
                    'request_id': requirement_id,
                    'data': {'status': 'extracting_sources', 'message': f"Updating Criteria scores using formuala....."}
                })

                #calculate criteria that uses formula and replaces scores
                processor = CriteriaFormulaProcessor(self.formula_criteria_weights)
                scores_explanation = ""
                try:
                  
                  processed_data,scores_explanation = processor.process_data(full_log)
                  full_log = processed_data
                except Exception as e:
                  print(f"Error updating scores with formula : {e}")

                self.add_event(requirement_id, 'progress_message', {
                    'event_type': 'CBP',
                    'request_id': requirement_id,
                    'data': {'status': 'extracting_sources', 'message': f"Generating comparison table..."}
                })

                
              
                table_creator = ComparisonTableCreator(full_log, self.criteria_weights,scores_explanation)
                html_table,raw_data,bidder_summary = table_creator.create_complete_html_table()
                technical_criteri_eval = extract_criteria_and_reasons(full_log)
                full_log.append({"Criteria_formula_processing": scores_explanation})
                full_log.append({"comparison_table": html_table})


                self.data_aggregrated_report['comparison_table'] = raw_data
                self.data_aggregrated_report["technical_criteria_names"] = self.technical_criteria_names
                self.data_aggregrated_report["bidder_summary"] = bidder_summary
                self.data_aggregrated_report["technical_criteria_eval"] = technical_criteri_eval

                full_log.append({"data_aggregrated_report": self.data_aggregrated_report})
                print(f"UPDATING AGGREGRATED DATA FOR REPPRT")
                Requirement.update(requirement_id, data_aggregrate_report=json.dumps({"data_aggregrated_report": self.data_aggregrated_report}))
                
                # Save HTML to file
                
                # with open('comparison_table.html', 'w') as f:
                #   f.write(html_table)
                
            except Exception as e:
                print(f"Error creating comparison table: {e}")

            # Emit completion event with full results
            self.add_event(requirement_id, 'completed_event', {
                'event_type': 'CBP',
                'request_id': requirement_id,
                'data': {
                    'full_log': full_log,
                    'bid_count': len(bids)
                }
            })
    
    
            # output_file = f"cbp_full_log_{requirement_id}.txt"
            # with open(output_file, 'w') as f:
            #     f.write("Chronobid Processing Full Log\n")
            #     f.write("============================\n\n")
            #     f.write(json.dumps(full_log, indent=4))
            #     f.write(f"\n\nTotal bids processed: {len(bids)}\n")
            #     f.write(f"Processing completed at: {time.strftime('%Y-%m-%d %H:%M:%S')}\n")
    
            return full_log
            
        except Exception as e:
            print(f"Error in handle_multiple_bids: {e}")
            Requirement.update(requirement_id, status='idle', tried=(original_requirement.get("tried", 0) + 1))
            return None
        
    
    def _process_single_bid(self, original_requirement, requirement_id, bid_info, bid_id):
        """Process a single bid in a separate greenlet"""
        from init import app  # Import Flask app
        
        try:
            # Create new app context for this greenlet
            with app.app_context():
                # Duplicate the requirement for this bid
                dup_requirement = original_requirement.copy()
                bid_requirement_id = f"{requirement_id}___{bid_id}"
                dup_requirement['id'] = bid_requirement_id
                dup_requirement['original_id'] = requirement_id
                dup_requirement['bid_data'] = bid_info
                dup_requirement['bid_index'] = bid_info.get('bid_index', 0)
                dup_requirement['bidder_name'] = bid_info.get('bidder_name', f'Bidder {bid_info.get("bid_index", 0)}')
                
                # Notify about processing current bid
                self.add_event(requirement_id, 'progress_message', {
                    'event_type': 'CBP',
                    'request_id': requirement_id,
                    'data': {'status': 'processing_bid', 'message': f"Processing bid from {dup_requirement['bidder_name']}..."}
                })
                
                # Process single bid
                result = None
                max_retries = 3
                retry_count = 0
                while result is None and retry_count < max_retries:
                    result = self.handle_single_new_document_v2_sync(bid_requirement_id)
                    if result is None:
                        retry_count += 1
                        print(f"Retry {retry_count} for bid {bid_requirement_id}")
                
                if result:  # If the bid processing was successful
                    # Add bid-specific info to the log
                    result['bid_index'] = bid_info.get('bid_index')
                    result['bid_requirement_id'] = bid_requirement_id
                    result['bidder_name'] = bid_info.get('bidder_name')
                    result['bid_id'] = bid_id
                    result['status'] = 'completed'
                    return result
                else:
                    return {
                        "bid_index": bid_info.get('bid_index'),
                        "bid_requirement_id": bid_requirement_id,
                        "bid_id": bid_id,
                        "bidder_name": bid_info.get('bidder_name'),
                        "error": "Processing failed",
                        "status": "failed"
                    }
                
        except Exception as e:
            import traceback
            print(f"Error processing bid {bid_id}:")
            traceback.print_exc()
            return {
                "bid_index": bid_info.get('bid_index'),
                "bid_requirement_id": bid_requirement_id,
                "bid_id": bid_id,
                "bidder_name": bid_info.get('bidder_name'),
                "error": str(e),
                "status": "failed"
            }


    async def handle_single_new_document_v2(self, requirement_bid_ids):
        try:
            print(f"Received requirement_bid_ids: {requirement_bid_ids}")
            # Get the requirement to check if it's a bid
            requirement_id = requirement_bid_ids.split("___")[0]
            print(f"Extracted requirement_id: {requirement_id}")
            requirement = Requirement.get_single(requirement_id)
            self.exist_bid = requirement_bid_ids.split("___")[1]
        
            print(f"Extracted exist_bid: {self.exist_bid}")
            
            # Check if the requirement status is already done or has been tried too many times
            if requirement['status'] == 'done':
                print(f"CBP Requirement with id: {requirement_id} is already done. Exiting...")
                return None
                
            if requirement.get('tried', 0) > 1:
                print(f"CBP Requirement with id: {requirement_id} has been tried more than once. Exiting...")
                return None

            # Get bid-specific information if this is a bid
            
            bid_info = [x for x in self.multi_bid_info if x["id"]== self.exist_bid][0]

            bid_specific_info = {
                'bid_index': bid_info.get('bid_index'),
                'bidder_name': bid_info.get('bidder_name'),
                "bid_id":bid_info.get('id')
            }

            print(f"Bid info: {bid_specific_info}")

            self.add_event(requirement_id, 'progress_message', {
                'event_type': 'CBP',
                'request_id': requirement_id,
                'data': {'status': 'extracting_sources', 'message': f"Welcome to Chronobid Assistant..."}
            })
 
            self._prepare_context(requirement)
            self.report_manager = ReportManager(
                self.env_data.get('CHRONOBID_DIR'), 
                self.requirement["project_id"], 
                requirement_id,
                bid_id=self.exist_bid
            )
            print(f"Report manager initialized for project_id: {self.requirement['project_id']} and bid_id: {self.exist_bid}")

            # Initialize log with bid information if available
            initial_log = self._initialize_log(len(self.criteria))
            if bid_specific_info:
                initial_log.update(bid_specific_info)
            print(f"Initial log created: {initial_log}")
            
            self.report_manager.create_initial_report(initial_log)
            log = self.report_manager.load_report()
            print(f"Loaded report: {log}")

            for idx, criteria in enumerate(self.criteria, start=1):
                
                await self._async_process_single_criterion(criteria, idx, log)

            # Add bid information to the final log if this is a bid
            if bid_specific_info:
                log.update(bid_specific_info)
                print(f"Updated log with bid info: {log}")

            self.add_event(requirement_id, 'progress_message', {
                'event_type': 'CBP',
                'request_id': requirement_id,
                'data': {'status': 'Finishing up a specific bid....', 'message': f"Completed analysis for bid : {bid_specific_info.get('bidder_name')}..."}
            })
            print(f"Completed event added for requirement_id: {requirement_id}")

            log = self.report_manager.load_report()
            print(f"Full log: {log}")
            return log
            #await self._finalize_log_and_status(log)

        except Exception as e:
            print(f"Error: {e}")
            return None
        
    def handle_single_new_document_v2_sync(self, requirement_bid_ids):
        try:
            print(f"Received requirement_bid_ids: {requirement_bid_ids}")
            # Get the requirement to check if it's a bid
            requirement_id = requirement_bid_ids.split("___")[0]
            print(f"Extracted requirement_id: {requirement_id}")
            requirement = Requirement.get_single(requirement_id)
            self.exist_bid = requirement_bid_ids.split("___")[1]
        
            print(f"Extracted exist_bid: {self.exist_bid}")
            
          

            # Get bid-specific information if this is a bid
            
            bid_info = [x for x in self.multi_bid_info if x["id"]== self.exist_bid][0]

            bid_specific_info = {
                'bid_index': bid_info.get('bid_index'),
                'bidder_name': bid_info.get('bidder_name', f'Bidder {bid_info.get("bid_index", 0)}'),
                "bid_id":bid_info.get('id')
            }

            print(f"Bid info: {bid_specific_info}")

            self.add_event(requirement_id, 'progress_message', {
                'event_type': 'CBP',
                'request_id': requirement_id,
                'data': {'status': 'extracting_sources', 'message': f"Welcome to Chronobid Assistant..."}
            })
 
            self._prepare_context(requirement)
            self.report_manager = ReportManager(
                self.env_data.get('CHRONOBID_DIR'), 
                self.requirement["project_id"], 
                requirement_id,
                bid_id=self.exist_bid
            )
            print(f"Report manager initialized for project_id: {self.requirement['project_id']} and bid_id: {self.exist_bid}")

            # Initialize log with bid information if available
            initial_log = self._initialize_log(len(self.criteria))
            if bid_specific_info:
                initial_log.update(bid_specific_info)
            # print(f"Initial log created: {initial_log}")
            self.criteria_weights = [] # also ick sub critera on each vriyeria

            self.report_manager.create_initial_report(initial_log)
            log = self.report_manager.load_report()
            # print(f"Loaded report: {log}")
            print(f"\033[93mChecking all criteria name: {self.criteria}\033[0m")

            for idx, criteria in enumerate(self.criteria, start=1):
                print(f"Processing criterion {idx}: {criteria}")

                self.criteria_weights.append({"criteria": criteria['name'], "weight": criteria['weight']})
                main_criterion_dict = {k: v for k, v in criteria.items() if k != 'sub_criteria'}
                if "formula" in main_criterion_dict and main_criterion_dict["formula"]:
                    self.formula_criteria_weights[main_criterion_dict["name"]] = main_criterion_dict['weight']
                sub_criteria_list = criteria.get('sub_criteria', [])


                if sub_criteria_list:
                    for sub_criteria in sub_criteria_list:
                        self.criteria_weights.append({"criteria": sub_criteria['name'], "weight": sub_criteria['weight']})
                max_retries = 1
                for attempt in range(max_retries):
                    try:
                        #print this in yellow
                        print(f"\033[93mChecking criteria name: {criteria['name']}\033[0m")

                        if criteria["name"] == "technical_evaluation_criteria":

                            self.criteria_weights.append({"criteria":"Technical Criteria", "weight": criteria['weight']})
                            self.process_technical_criteria(criteria, idx, log,bid_info)
                            self.technical_criteria_names.append("Technical Criteria")

                        else:
                          
                          self.sync_process_regular_criterion(main_criterion_dict,sub_criteria_list, idx, log, bid_info)
                        
                        break  # If successful, break out of the retry loop
                    except Exception as e:
                        print(f"Error processing criterion {idx} (attempt {attempt + 1}): {e}")
                        if attempt == max_retries - 1:
                            print(f"Failed to process criterion {idx} after {max_retries} attempts")

            # Add bid information to the final log if this is a bid
            if bid_specific_info:
                log.update(bid_specific_info)
                # print(f"Updated log with bid info: {log}")

            self.add_event(requirement_id, 'progress_message', {
                'event_type': 'CBP',
                'request_id': requirement_id,
                'data': {'status': 'Finishing up a specific bid....', 'message': f"Completed analysis for bid : {bid_specific_info.get('bidder_name')} with total of {len(sub_criteria_list)} subcriteria..."}
            })
            print(f"Completed event added for requirement_id: {requirement_id}")

            log = self.report_manager.load_report()
            # print(f"Full log: {log}")
            return log
            #await self._finalize_log_and_status(log)

        except Exception as e:
            import traceback
            traceback.print_exc()
            print(f"Error: {e}")
            return None
        
    def sync_process_regular_criterion(self, criteria, sub_criteria, count, log, bid_info={}):
        try:
            # Create a unique key for this bid and criteria combination
            bid_id = bid_info.get('id', '')
            criteria_key = f"{bid_id}_{count}"
            
            # Check if this criteria has already been processed for this bid
            if bid_id in self.processed_criteria_tracker and count in self.processed_criteria_tracker[bid_id]:
                print(f"Skipping already processed criterion {count} for bid {bid_info.get('bidder_name')}")
                return
            
            # Initialize the tracker for this bid if it doesn't exist
            if bid_id not in self.processed_criteria_tracker:
                self.processed_criteria_tracker[bid_id] = set()
            
            # Mark this criteria as processed for this bid
            self.processed_criteria_tracker[bid_id].add(count)
            
            print(f'now processing dataset1 for criteria {count}....')
            start_time = time.time()  # Start time for dataset1 processing

            self.add_event(self.requirement_id , 'progress_message', {
                'event_type': 'CBP',
                'request_id': self.requirement_id ,
                'data': {'status': 'extracting_sources', 'message': f"Analyzing Bid {bid_info.get('bidder_name')} Criterion {count} ({criteria.get('name')}). Extracting relevant  sources.", 'query_idx': count}
            })
            
            # Only extract if we haven't already processed this criteria
            # Check if the criteria has already been processed

            if not self.tender_relevant_chunks.get(criteria["name"]):
                print(f"[INFO] Extracting for new criteria: {criteria['name']}")
                
                source_text = self.extract_main_and_sub_documents_tender(
                    self.project_id,
                    criteria,
                    sub_criteria
                )

                # Save the result to prevent reprocessing in future calls
                self.tender_relevant_chunks[criteria["name"]] = source_text
                print(f"[INFO] Extraction complete and cached for: {criteria['name']}")
            else:
                print(f"[INFO] Using cached result for criteria: {criteria['name']}")
                source_text = self.tender_relevant_chunks[criteria["name"]]
            
            # with open(f"criteria_docs_{bid_id}.json", "a") as f:
            #     json.dump({
            #         "criteria_name": criteria.get('name'),
            #         "criteria_chunk_tender": source_text
            #     }, f)
            #     f.write("\n")
                  
            #extra tender documents
            if not source_text:
                raise RuntimeError("Dataset 1 extraction failed")
            

            
            log['source_potential_data_chunks'] = source_text
            
            print(f"SOURCE DOCUMENTS: {source_text}")
            # Time taken for dataset1
            dataset1_time = time.time() - start_time
            print(f"Time taken for Dataset1 for criteria {count} extraction: {dataset1_time:.2f} seconds")

            print(f'now processing dataset2.... for criteria {count}.')
            start_time = time.time()  # Start time for dataset2 processing

            self.add_event(self.requirement_id , 'progress_message', {
                'event_type': 'CBP',
                'request_id': self.requirement_id ,
                'data': {'status': 'extracting_sources', 'message': f"Working on  Bid {bid_info.get('bidder_name')} Criterion {count} ({criteria.get('name')}). Discovering insights", 'query_idx': count}
            })

            criteria_data = {
                        'main_criteria': criteria,
                        'main_document': source_text["main_document"],
                        'sub_criteria': sub_criteria,
                        'sub_documents': source_text["sub_documents"]
                    }
            #-----

            """
            try:
                # Read existing data if file exists
                existing_data = []
                try:
                    with open(f"criteria_docs_{bid_id}.json", "r") as f:
                        for line in f:
                            if line.strip():  # Skip empty lines
                                existing_data.append(json.loads(line))
                except FileNotFoundError:
                    pass  # File doesn't exist yet, that's okay

                # Append new data
                existing_data.append({
                    "criteria_name": criteria.get('name'),
                    "criteria_chunk_tender": criteria_data
                })

                # Write back all data
                with open(f"criteria_docs_{bid_id}.json", "w") as f:
                    for item in existing_data:
                        json.dump(item, f)
                        f.write("\n")
            except Exception as e:
                print(f"Error handling criteria docs file: {e}")
                # Continue execution even if file handling fails
                """
        #---------
           #search data on bids
           #start 
            '''query = f"{criteria['name']}\n{criteria['description']}"
            search_queries = [query] + [""]
            query = f"Question {count}\nTitle: {criteria['name']}\nDescription: {criteria['description']}"
            def format_uplaod_documents(res):
                formatted_results = []
                for item in res[0]:
                    text_content = item[0]
                    score = item[1]
                    source_doc = item[2] if len(item) > 2 else "Unknown Source"
                    
                    formatted_results.append({
                        "text_content": text_content,
                        "document_name": source_doc
                    })
                return formatted_results
            
            bid_chunks = self.faiss_processor.search_sync([search_queries[0]], self.requirement_metadata.get('bid_id'), 40)
            bid_chunks_formatted = format_uplaod_documents(bid_chunks)

            bid_chunks = {"main_criteria"}
            if 'sub_criteria' in criteria  and criteria['sub_criteria']:
                for sub_criterion in criteria['sub_criteria']:
                    sub_query = f"{sub_criterion['name']}\n{sub_criterion['description']}"
                    sub_bid_chunks = self.faiss_processor.search_sync([sub_query], self.requirement_metadata.get('bid_id'), 40)
                    sub_bid_chunks_formatted = format_uplaod_documents(sub_bid_chunks)
                    bid_chunks_formatted.extend(sub_bid_chunks_formatted)

            docs = {

                "SOURCE_DOCUMENT":criteria_data,
                "UPLOADED_DOCUMENT":bid_chunks
            }
            self.chronobid_reasoning.generate_criteria_summaryv2(
                main_criteria=criteria,
                sub_criteria=sub_criteria,
                evaluation_docs=docs,
                log=log,
                criteria_index=count,
                bidder_name=bid_info.get('bidder_name')
            )'''

            
            data_output = self.dataset2_extractor.process_hierarchical_criteria_evaluation(
                            criteria_data=criteria_data,
                            metadata=self.requirement_metadata,
                            count=count,
                            log=log,
                            bid_id= bid_id
                        )
            
            main_criteria_docs, sub_criteria_docs = self.dataset2_extractor.generate_evaluation_texts(data_output,source_text)

            print(f"main_criteria_docs: {main_criteria_docs}")

           # Save to a file
            # with open("main_criteria_docs.json", "w", encoding="utf-8") as f:
            #     json.dump(main_criteria_docs, f, ensure_ascii=False, indent=4)

            print(f"sub_criteria_docs: {sub_criteria_docs}")

            docs = {
                "main_criteria": main_criteria_docs,
                "sub_criteria": sub_criteria_docs
            }

            
            print(f'completed dataset2 for criteria {count}...')
            self.add_event(self.requirement_id , 'progress_message', {
                'event_type': 'CBP',
                'request_id': self.requirement_id ,
                'data': {'status': 'extracting_sources', 'message': f"Surfacing Insights on  Bid {bid_info.get('bidder_name')} Criterion {count} ({criteria.get('name')}) Analyzing document sections", 'query_idx': count}
            })
            self.chronobid_reasoning.generate_criteria_summary(
                main_criteria=criteria,
                sub_criteria=sub_criteria,
                evaluation_docs=docs,
                log=log,
                criteria_index=count,
                bidder_name=bid_info.get('bidder_name')
            )
            

            self.report_manager.update_log(log)

            
            self.add_event(self.requirement_id, 'in_progress_event', {
                'event_type': 'CBP',
                'request_id': self.requirement_id,
                'data': log
            })

            #print(f"HERE IS MY LOG : {log}")
            # Time taken for _sync_process_single_criterion
            processing_time = time.time() - start_time
            print(f"Time taken for _sync_process_single_criterion: {processing_time:.2f} seconds")
            gevent.sleep(5)
        except Exception as e:
            print(f"Error in criterion {count}: {e}")
            self.add_event(self.requirement_id, 'error_event', {
                        'event_type': 'CBP',
                        'request_id': self.requirement_id,
                        'data': {'status': 'error', 'message': f"Oops! Something went wrong for Criterion {criteria} : {count}", 'error': str(e)}
                    })
            
            

    def _prepare_context(self, requirement):
        self.requirement = requirement
        self.requirement_id = requirement["id"]

        self.requirement_metadata =  {"exist_bid":True,"bid_id":self.exist_bid}
        print(f"loaded metadata for req_id {self.requirement_id} : {json.dumps(self.requirement_metadata)}")

        # Add error handling for criteria parsing
        try:
            self.criteria = json.loads(requirement["criteria"])
        except (json.JSONDecodeError, KeyError) as e:
            print(f"Error parsing criteria for requirement_id {self.requirement_id}: {e}")
            self.criteria = []

        # Add error handling for name parsing
        try:
            filenames = []
            bid_files = BidFile.get_by(bid_id=self.exist_bid)
            print(f"Bid files : {bid_files} ")
            for bid_file in bid_files:
               filenames.append(bid_file['name'])
            self.requirement_name = filenames
            print(f"Requirement names: {self.requirement_name}")
            

        except (json.JSONDecodeError, KeyError) as e:
            print(f"Error parsing name for requirement_id {self.requirement_id}: {e}")
            self.requirement_name = []


        self.tender_number = requirement["id"]
        self.bidder_name = '[BIDDER NAME]'
        self.project = Project.get_single(requirement["project_id"])
        self.project_id = requirement['project_id']
        self.tender_title = self.bidder_name
        self.file_base_path = os.path.join(self.env_data.get('CHRONOBID_DIR'), self.project_id)
        self.intro_texts = {
            "risk": "Overall, [BIDDER NAME] presents certain risks in relation to criteria [#], which may impact the project's successful completion.",
            "strength": "Overall, [BIDDER NAME] has demonstrated a good understanding and evidence of criteria [#].",
            "weakness": "Overall, [BIDDER NAME] has notable weaknesses in addressing criteria [#], lacking sufficient evidence."
        }
        self.top_text = f'''
            <div>
                <h2>Evaluation Result</h2>
                <p>Following a detailed evaluation of <strong>{self.bidder_name}</strong>'s proposal, the offer achieved an overall score of <span class='score'><Overall_Score>0.0</Overall_Score>%</span> . This score reflects the performance across all assessed criteria. The summary of the evaluation per criterion is as follows:</p>
            </div>
        '''
        self.evaluation_result_text = f'''
            Following the thorough evaluation of [BIDDER NAME]'s proposal, an overall weighted score of <Overall_Score>0.0</Overall_Score>% was achieved, reflecting performance across all assessed criteria. 
            The detailed scoring for each criterion is as follows:
        '''

    def _initialize_log(self, criteria_count):
        return {
            "criteria": "",
            "input_token": 0,
            "output_token": 0,
            "requirement_id": self.requirement_id,
            "project_id": self.project_id,
            "chunk_to_pass": [""] * criteria_count,
            "evaluate_summaries": [""] * criteria_count,
            "top_text": self.top_text,
            "uploaded_text": "",
            "evaluate_summary": "",
            "evaluate_summary_processing": [""] * criteria_count,
            "evaluate_summary_intro" : self.top_text,
            "evaluate_summary_chunk": [""] * criteria_count, 
            "evaluate_summary_score": "",
            "source_potential_data_chunks": "",
            "evaluation_report_section": {
                "cover_page":"",
                "introduction":"",
                "evaluation_result": self.evaluation_result_text,
                "detailed_evaluation":"",
                "recommendation":"",
                "strength_chunk":[""] * criteria_count, "weak_chunk":[""] * criteria_count, "risk_chunk":[""] * criteria_count,
                "strength_text":"","weak_text":"","risk_text":"",
                "strength_weak_risk_chunk_intro":{},
                "evaluation_report":""
            }
        }
    
    def _remove_extension(self, file_name):
        base_name = os.path.splitext(file_name)[0]
        pattern = r'_(\d{6})_merged$'        
        modified_name = re.sub(pattern, '', base_name)
        
        return modified_name
    
    
    async def _sync_process_single_criterion(self, criteria, count, log): 
        try:
            start_time = time.time()  # Start time for the whole processing

            chunks = await self._get_chunks_to_process(count, log)
            log['chunk_to_pass'][count-1] = chunks
            print(f"Criterian {count} chunks: {chunks}")


            evaluate_text = f"<CRITERIA>Criteria {count}<CRITERIA><br/>"
            for chunk in chunks:
                evaluate_text += f"""
                    <Evaluation>{chunk['evaluation_data']['Evaluation']}</Evaluation><br/>
                    <Score>{chunk['evaluation_data']['Score']}</Score><br/>
                    <Reason>{chunk['evaluation_data']['Reason']}</Reason><br/>
                """
            log['evaluate_summary_processing'][count-1] = f"{evaluate_text}"

            # print(f"Evaluation summary for criteria {count}: {evaluate_text}")

            print(f'processing evaluation summary for criteria {count}...')
            self.add_event(self.requirement_id , 'progress_message', {
                'event_type': 'CBP',
                'request_id': self.requirement_id ,
                'data': {'status': 'extracting_sources', 'message': f"Compiling the evaluation summary for Query {count}. Finalizing results.", 'query_idx': count}
            })
            
            await self.chronobid_reasoning.get_claude_to_summarize_evaluate_v2(log, criteria, count)

            print('came back from claude evaluate summary...')        
            
            self.add_event(self.requirement_id, 'in_progress_event', {
                'event_type': 'CBP',
                'request_id': self.requirement_id,
                'data': log
            })

            # Time taken for _sync_process_single_criterion
            processing_time = time.time() - start_time
            print(f"Time taken for _sync_process_single_criterion: {processing_time:.2f} seconds")
    
        except Exception as e:
            print(f"Error in processing criterion {count}: {e}")

   

    async def _async_process_single_criterion(self, criteria, count, log):
        try:

            print(f'now processing dataset1 for criteria {count}....')
            start_time = time.time()  # Start time for dataset1 processing

            self.add_event(self.requirement_id , 'progress_message', {
                'event_type': 'CBP',
                'request_id': self.requirement_id ,
                'data': {'status': 'extracting_sources', 'message': f"Starting analysis for Query {count}. Extracting relevant tender sources.", 'query_idx': count}
            })
            print('finished adding event...')

            source_text = await self._extract_dataset1(criteria, count)
            if not source_text:
                raise RuntimeError("Dataset 1 extraction failed")
            
            log['source_potential_data_chunks'] = source_text
            
            # Time taken for dataset1
            dataset1_time = time.time() - start_time
            print(f"Time taken for Dataset1 for criteria {count} extraction: {dataset1_time:.2f} seconds")

            print(f'now processing dataset2.... for criteria {count}.')
            start_time = time.time()  # Start time for dataset2 processing

            self.add_event(self.requirement_id , 'progress_message', {
                'event_type': 'CBP',
                'request_id': self.requirement_id ,
                'data': {'status': 'extracting_sources', 'message': f"Continuing analysis for Query {count}. Refining insights from the uploaded bid.", 'query_idx': count}
            })
            data = await self._extract_dataset2(criteria, source_text, count, log)
            if not data:
                raise RuntimeError("Dataset 2 extraction failed")

            # Time taken for dataset2
            dataset2_time = time.time() - start_time
            print(f"Time taken for Dataset2 extraction: {dataset2_time:.2f} seconds")
            
            print(f'completed dataset2 for criteria {count}...')
            self.add_event(self.requirement_id , 'progress_message', {
                'event_type': 'CBP',
                'request_id': self.requirement_id ,
                'data': {'status': 'extracting_sources', 'message': f"Insights for Query {count} extracted. Preparing to analyze document chunks.", 'query_idx': count}
            })

            await self._sync_process_single_criterion(criteria, count, log)
            self.report_manager.update_log(log)

        except Exception as e:
            print(f"Error in criterion {count}: {e}")


    
    def process_technical_criteria(self, criteria, count, log,bid_info):
        try:

            # Check if this technical criteria has already been processed for this bid
            bid_id = bid_info.get('id', '')
            
            # Initialize the tracker for this bid if it doesn't exist
            if bid_id not in self.processed_criteria_tracker:
                self.processed_criteria_tracker[bid_id] = set()
            
            # Check if this criteria has already been processed
            if count in self.processed_criteria_tracker[bid_id]:
                print(f"Skipping already processed technical criterion {count} for bid {bid_id}")
                return
            
            # Mark this criteria as processed BEFORE any processing begins
            self.processed_criteria_tracker[bid_id].add(count)
            
            # Increment the technical criteria bid counter
            self.technical_criteria_bid_counter += 1
            print(f"Processing technical criterion {count} for bid {bid_id} (Counter: {self.technical_criteria_bid_counter})")
            
            # Check if we've processed all bids
            total_bids = len(self.multi_bid_info)
            if self.technical_criteria_bid_counter > total_bids:
                print(f"Warning: Technical criteria bid counter ({self.technical_criteria_bid_counter}) exceeds total bids ({total_bids})")
                return
            
            def merge_extra_items_into_equipment(equipment_list, equipment_data):
                """
                Adds extra_items into the categories of each equipment based on matching ID.
                
                Parameters:
                - equipment_list: List of equipment dicts with existing 'categories'.
                - equipment_data: List of equipment dicts with 'id' and 'extra_items'.
                """
                
                # Create a map of equipment ID to extra items for quick lookup
                extra_items_map = {}
                for equipment in equipment_data:
                    if "extra_items" in equipment and equipment["extra_items"]:
                        equipment_id = equipment.get("id")
                        if equipment_id:
                            extra_items_map[equipment_id] = equipment["extra_items"]
                
                for equipment in equipment_list:
                    equipment_id = equipment.get("id")
                    if equipment_id in extra_items_map:
                        extra_items = extra_items_map[equipment_id]
                        
                        # If categories doesn't exist, initialize it
                        if "categories" not in equipment:
                            equipment["categories"] = []
                            
                        # Add each extra item category to the equipment's categories
                        for category in extra_items:
                            category_name = category.get("category")
                            items = category.get("items", [])
                            
                            # Build category structure as expected in `categories`
                            new_category = {
                                "name": category_name,
                                "items": items
                            }
                            
                            # Add the new category to the equipment's categories
                            equipment["categories"].append(new_category)
                            
                return equipment_list
            
            evaluator = TechnicalCriteriaEvaluator(
                requirement_id=self.requirement_id,
                socket_manager=self.socket_manager
            )
            
            
            # Get the overall criteria weight
            criteria_weight = criteria["weight"]
            
            # Parse equipment IDs with their weights
            equipment_data = criteria["equipment_ids"]
            print(f"Extracting equipment list with weights...{equipment_data}")
            
            # Extract equipment IDs for database query
            equipment_ids = [item["id"] for item in equipment_data]
            
            # Get equipment details from database
           
            equipment_list = EquipmentPackage.get_equipment_with_specs(
                equipment_package_ids=equipment_ids
            )
            print("Equipment list extracted: ", equipment_list)
            
            if not equipment_list:
                print(f"Warning: Equipment list is empty for criteria {count}")
                error_message = f"""
                    <div>
                        <h3 style="font-size: 18px; margin-bottom: 8px;">Criteria {count}: Technical Criteria</h3>
                        <p style="font-size: 14px; color: #cc0000; margin: 0 0 4px 0;">No Equipment Information Found in database</p>
                        <p style="font-size: 14px; margin: 0 0 4px 0;">No equipment data was found for the equipments selected for the technical criteria. Please ensure that the equipment has been properly selected and configured for this technical criteria.</p>
                    </div>
                    """
                log['evaluate_summary_chunk'][count-1] = error_message
                self.report_manager.update_log(log)
                self.add_event(self.requirement_id, 'in_progress_event', {
                    'event_type': 'CBP',
                    'request_id': self.requirement_id,
                    'data': log
                })
                return
            
            # Create a mapping of equipment IDs to their weights
            equipment_weights = {item["id"]: item["weight"] for item in equipment_data}
        
            # Add equipment criteria weights to the criteria_weights list
            equip_names_weights = []
            for equipment in equipment_list:
                equipment_id = equipment.get("id")
                equipment_name = equipment.get("equipment_name")
                # Use the weight from equipment_weights if available, otherwise use a default
                weight = equipment_weights.get(equipment_id, 0)
                # Clean equipment name by replacing \xa0 with regular space
                clean_equipment_name = equipment_name.replace('\xa0', ' ')
                equip_names_weights.append({"equipment_name": clean_equipment_name, "weight": weight})
                self.criteria_weights.append({"criteria": clean_equipment_name, "weight": weight})
                self.technical_criteria_names.append(clean_equipment_name)
            
          
            equipment_list = merge_extra_items_into_equipment(equipment_list, equipment_data)

            print(f"MERGED EQUIPMENT LIST: {equipment_list}")
            # Call the evaluator with the equipment list and weights
            evaluator.evaluate(
                equipment_list=equipment_list,
                tender_id=self.project_id,
                bid_id=bid_id,
                criteria_index=count,
                log=log,
                equipment_name_weights = equip_names_weights,
                technical_criteria_weight=criteria_weight
            )
            
            self.report_manager.update_log(log)
            self.add_event(self.requirement_id, 'in_progress_event', {
                'event_type': 'CBP',
                'request_id': self.requirement_id,
                'data': log
            })
            print(f"Equipment weights: {self.criteria_weights}")
        except Exception as e:
            print(f"Error in technical criteria {count}: {e}")
            self.add_event(self.requirement_id, 'error_event', {
                'event_type': 'CBP',
                'request_id': self.requirement_id,
                'data': {'status': 'error', 'message': f"Oops! Something went wrong for Criterion: {count}, Technical Criteria", 'error': str(e)}
            })


    async def _extract_dataset1(self, criteria, count):
        print('i enter_extract_dataset1 method...')
        extractor = Dataset1Extractor(criteria)
        return await extractor.get_dataset1(self.project_id, count)
    
    def extract_main_and_sub_documents_tender(self,project_id, main_criteria, sub_criteria_list):
        """
        Extracts relevant chunks for both main and sub criteria.

        Args:
            project_id (str): ID of the project/document set.
            main_criteria (dict): Dictionary containing the main criteria for extraction.
            sub_criteria_list (list): List of dictionaries, each representing sub-criteria.

        Returns:
            dict: A dictionary containing extracted content from main and sub criteria.
                {
                    'main_document': ...,
                    'sub_documents': [...]
                }
        """
        result = {
            'main_document': None,
            'sub_documents': []
        }

        # Process main criteria
        try:
            main_extractor = Dataset1Extractor(main_criteria)
            result['main_document'] = main_extractor.extract_tender_relevnt_chunks(project_id, main_criteria)
        except Exception as e:
            print(f"Error processing main criteria: {e}")
            result['main_document'] = None

        # Process sub-criteria
        for sub_criteria in sub_criteria_list:
            try:
                sub_extractor = Dataset1Extractor(sub_criteria)
                sub_result = sub_extractor.extract_tender_relevnt_chunks(project_id, sub_criteria)
                result['sub_documents'].append(sub_result)
            except Exception as e:
                print(f"Error processing sub-criteria: {e}")
                result['sub_documents'].append(None)

        return result

    async def _extract_dataset2(self, criteria, source_text, count, log):
        return await self.dataset2_extractor.process_new_file_Chronobid(
            criteria, source_text, self.requirement_metadata, count, log
        )
    def _extract_dataset1_sync(self, criteria,sub_criteria ,count):
        print('i enter_extract_dataset1 method...')
        extractor = Dataset1Extractor(criteria)
        return extractor.get_dataset1_sync_latest(self.project_id,count,sub_criteria)
    
    def extract_tender_requirements(self, criteria, count):
        return self.dataset2_extractor.extract_tender_requirements(criteria, count)
    
    def _extract_dataset2_sync(self, criteria, sub_criteria,count ):

        extractor = Dataset1Extractor(criteria)
        return extractor.get_dataset1_sync_latest(self.project_id,count,sub_criteria)
        
    async def _get_chunks_to_process(self, count, log):
        for key in ['strength_chunk', 'weak_chunk', 'risk_chunk']:
            print(f"this is the key: {key} and log: {log['evaluation_report_section'][key]} ")
            chunk_list = log['evaluation_report_section'].get(key, [])
            print(f"condition check: {chunk_list}")
            if len(chunk_list) > count - 1 and chunk_list[count - 1]:
                print('the chunklist: ', chunk_list[count - 1])
                return chunk_list[count - 1]
        # Return an empty list if no valid chunk is found
        return []

    def _get_chunks_to_process_sync(self, count, log):
        for key in ['strength_chunk', 'weak_chunk', 'risk_chunk']:
            # print(f"this is the key: {key} and log: {log['evaluation_report_section'][key]} ")
            chunk_list = log['evaluation_report_section'].get(key, [])
            # print(f"condition check: {chunk_list}")
            if len(chunk_list) > count - 1 and chunk_list[count - 1]:
                # print('the chunklist: ', chunk_list[count - 1])
                return chunk_list[count - 1]
        # Return an empty list if no valid chunk is found
        return []
    
    async def _finalize_log_and_status(self, log):
        self.report_manager.update_log(log)
        if not self.is_test:
            self.backend_status_updater.update_history_status(self.requirement_id, 2)

    def _finalize_log_and_status_sync(self, log):
        self.report_manager.update_log(log)
        if not self.is_test:
            self.backend_status_updater.update_history_status(self.requirement_id, 2)

    def add_event(self, request_id, event_name, data):
        print(f"Adding event {event_name} to room {request_id}")
        # return
        self.socket_manager.emit_to_client(request_id, event_name, data)




# Example usage of process_cbp
async def example_process_cbp():
    # Initialize the processor
    socket_manager = None  # Replace with actual socket manager if needed
    processor = ChronoMultibidDocumentProcessor(socket_manager)
    
    # Example requirement ID
    req_id = "example-requirement-id"
    
    # Example bid info
    bid_info = {
        "bid1": {
            "id": "bid1",
            "bid_index": 0,
            "bidder_name": "Company A"
        },
        "bid2": {
            "id": "bid2",
            "bid_index": 1,
            "bidder_name": "Company B"
        }
    }
    
    # Process the CBP documents
    result = await processor.process_cbp(req_id)
    
    # Save results to a text file
    if result:
        output_file = f"cbp_results_{req_id}.txt"
        with open(output_file, 'w') as f:
            f.write("Chronobid Processing Results\n")
            f.write("==========================\n\n")
            
            for bid_result in result:
                f.write(f"Bidder: {bid_result.get('bidder_name', 'Unknown')}\n")
                f.write(f"Bid Index: {bid_result.get('bid_index', 'N/A')}\n")
                f.write(f"Status: {bid_result.get('status', 'Unknown')}\n")
                f.write("-" * 50 + "\n")
                
                # Write evaluation details if available
                if 'evaluation_report_section' in bid_result:
                    eval_section = bid_result['evaluation_report_section']
                    f.write("\nEvaluation Report:\n")
                    f.write(eval_section.get('evaluation_report', 'No evaluation report available\n'))
                
                f.write("\n" + "=" * 50 + "\n\n")
        
        print(f"Results saved to {output_file}")
    else:
        print("No results to save")

# Run the example
if __name__ == "__main__":
    asyncio.run(example_process_cbp())
    
