import os
import asyncio
import traceback
from models import File
from services.process_document import FileProcessor
from services.data_handler import DataManager
from services.parser_openai_parellel import OpenAIProcessor

import json


class DocumentSeeder:
    def __init__(self):
        os.environ["CUDA_LAUNCH_BLOCKING"] = "1"
        self.data_manager = DataManager()
        self.file_processor = FileProcessor()
        self.open_ai_processor = OpenAIProcessor()

        # Get the path to the parent directory
        parent_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
        
        # Load environment variables from env.json file in the parent directory
        env_file_path = os.path.join(parent_dir, 'env.json')
        with open(env_file_path, 'r') as f:
            self.env_data = json.load(f)
    

    async def handle_single_document(self, file_id):
        try:
            file_entry = self._update_file_status(file_id, 'pending')
            # print('file_entry: ', file_entry)
            dirtry_map = {
                'chronobid': 'CHRONOBID_DIR',
                'specs': 'SPECS_DIR',
                'qmp': 'DATA_DIR'
            }
            dirtry_key = dirtry_map.get(file_entry["file_dirtry"], 'DATA_DIR')
            dirtry = self.env_data.get(dirtry_key)

            print('dirtry: ', dirtry)
            file_path = os.path.join(dirtry, file_entry["project_id"], file_entry["name"])
            file_type = os.path.splitext(file_path)[-1].lower().lstrip('.')
            
            # print("file path:", file_path)1
            data = await self.file_processor.process_file_for_seeding(file_path)
 
            
            if file_type in ["csv", "xls", "xlsx", "xlsm"]:
                merged_data = []
                page_number = 1
                # Assuming 'data' contains the list of dictionaries you provided
                for subsheet in data:
                    final_content = ""
                    for content in subsheet["content"]:
                        # Check if content exists and has at least one element
                        content_list = subsheet["content"][content]
                        if content_list and len(content_list) > 0:
                            text = str(content_list[0])
                            final_content += text
                            
                    merged_data.append({
                                    "title": subsheet["title"],  # Use the title as is
                                    "content": final_content,
                                    "page_number": subsheet["page_number"]  # Assign the correct page number
                                })
                    page_number += 1

                
                data = merged_data  # Replace 'data' with the merged_data after processing

                        
           # print('i am data: ', data)
            
            if not len(data):
                self._update_file_status(file_id, 'done')
                return 

            self._seed_data(file_entry["project_id"], data, file_id)
            self._update_file_status(file_id, 'done')
        except Exception as e:
            print(e)
            traceback.print_exc()
            self._handle_error(file_id)
    
    def handle_single_document_sync(self, file_id,file_name,project_type):
        try:
            import time
            start_time = time.time()
            print(f"Starting document processing for file_id: {file_id} at {start_time}")
            file_entry = self._update_file_status(file_id, 'pending')
           
            dirtry = self.env_data.get(file_entry["file_dirtry"], 'data')

            file_path = os.path.join(dirtry, file_entry["project_id"], file_entry["name"])
            file_type = os.path.splitext(file_path)[-1].lower().lstrip('.')
            
            # print("file path:", file_path)1
            data = self.file_processor.process_file_for_seeding_sync(file_path)
 
            
            if file_type in ["csv", "xls", "xlsx", "xlsm"]:
                merged_data = []
                page_number = 1
                # Assuming 'data' contains the list of dictionaries you provided
                for subsheet in data:
                    final_content = ""
                    for content in subsheet["content"]:
                        # Check if content exists and has at least one element
                        content_list = subsheet["content"][content]
                        if content_list and len(content_list) > 0:
                            text = str(content_list[0])
                            final_content += text
                            
                    merged_data.append({
                        "title": subsheet["title"],  # Use the title as is
                         "content": final_content,
                        "page_number": subsheet["page_number"]  # Assign the correct page number
                    })
                    page_number += 1

                
                data = merged_data  # Replace 'data' with the merged_data after processing

                        
            
            if not len(data):
                self._update_file_status(file_id, 'done')
                return  True,None
    
            print(f"Time taken before seeding: {time.time() - start_time:.2f} seconds")
            self.data_manager.seed_data_concurrently(file_entry["project_id"], data, file_id,file_name,project_type)

            # self._seed_data(file_entry["project_id"], data, file_id)
            self._update_file_status(file_id, 'done')
            end_time = time.time()
            total_time = end_time - start_time
            print(f"Document processing completed for file_id: {file_id} in {total_time:.2f} seconds")

            return True,None
        except Exception as e:
            print(e)
            traceback.print_exc()
            self._handle_error(file_id)
            return False,f"{e}"
    
    
    async def handle_single_uploaded_document(self, file_path, project_id, file_type):
        try:
            print('now processing pinecone seed for file: ', file_path)
            if file_type == 'pdf':
                data = await self.open_ai_processor.extract_text_from_pdf(file_path)
            else:
                data = await self.open_ai_processor.extract_text_from_docx(file_path)

            await self.data_manager.seed_upload_data(project_id, data[1])
            print('done processing the above file.... \n')
            
        except Exception as e:
            print(e)
            traceback.print_exc()

    async def handle_single_chronobid_document(self, file_id):
        try:
            file_entry = self._update_file_status(file_id, 'pending')
            dirtry = self.env_data.get('CHRONOBID_DIR')
            file_path = os.path.join(dirtry, file_entry["project_id"], file_entry["name"])
            # print("file path:", file_path)
            data = await self.file_processor.process_file_for_seeding(file_path)

            if not len(data):
                self._update_file_status(file_id, 'done')
                return 

            self._seed_data(file_entry["project_id"], data, file_id)
            self._update_file_status(file_id, 'done')
            
        except Exception as e:
            print(e)
            traceback.print_exc()
            self._handle_error(file_id)
    
    async def handle_single_specs_document(self, file_id):
        try:
            file_entry = self._update_file_status(file_id, 'pending')
            file_path = os.path.join(self.env_data.get('SPECS_DIR'), file_entry["project_id"], file_entry["name"])
            data = await self.file_processor.process_file_for_seeding(file_path)

            if not len(data):
                self._update_file_status(file_id, 'done')
                return 

            self._seed_data(file_entry["project_id"], data, file_id)
            self._update_file_status(file_id, 'done')
            
        except Exception as e:
            print(e)
            traceback.print_exc()
            self._handle_error(file_id)

    async def queue(self):
        pending = await self._get_files_by_status('pending')
        print('pending length: ', len(pending))
        if len(pending) >= 3:
            return
        
        files = sorted(await self._get_files_by_status('idle'), key=lambda x: x['tried'])
        if files:
            # Limit the batch size to 100
            batch_size = 1
            batch = files[:batch_size]
            
            # Filter files with `tried` less than 3
            batch = [file for file in batch if file['tried'] < 3]
            
            if batch:
                # Run tasks in parallel
                tasks = [asyncio.create_task(self.handle_single_document(file['id'])) for file in batch]                
                await asyncio.gather(*tasks)
                print(f"Processed {len(batch)} files in parallel.")
            else:
                print("No files eligible to process (tried >= 3).")
        else:
            print('No Files In Queue')


    async def process_all_documents(self):
        files = sorted(await self._get_files_by_status('idle'), key = lambda x: x['tried'])

        for x in files:
            await self.handle_single_document(x['id'])

    def _update_file_status(self, file_id, status):
        File.update(file_id, status=status)
        return File.get_single(file_id)

    def _seed_data(self, project_id, data, file_id):
        self.data_manager.seed_data(project_id, data, file_id)

    async def _get_files_by_status(self, status):
        return File.get_by(status=status, pdf_conversion_status='done')

    def _handle_error(self, file_id):
        file_entry = self._update_file_status(file_id, status='idle')
        File.update(file_id, tried=(file_entry["tried"] + 1))

if __name__ == "__main__":
    document_processor = DocumentSeeder()
    asyncio.run(document_processor.queue())
