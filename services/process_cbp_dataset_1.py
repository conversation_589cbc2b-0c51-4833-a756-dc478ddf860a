import gevent
from gevent import monkey
monkey.patch_all()
import asyncio
from services.data_handler import DataManager
from services.cohere_embedding import CohereService
from services.synonym_expansion import SynonymGenerator

from services.claude_ai_service import ClaudeService
from services.reasoning_chronobid import ChronobidReasoning

# New class for extracting dataset1
class Dataset1Extractor:
    def __init__(self, criteria):
        self.data_manager = DataManager()
        self.synonym_service = SynonymGenerator()
        self.claude_client = ClaudeService(claude_keys=['CLAUDE_API_CBP', 'CLAUDE_API_CBP'],default_key="CLAUDE_API_CBP")
        self.criteria = criteria
        self.chronobid_reasoning = ChronobidReasoning()
        


    async def get_dataset1(self, project_id, count):
        attempt = 0
        retries=3
        while attempt < retries:
            try:
                print('i enter dataset1 class')
                # Here we extract data source1 from Pinecone based on criteria
                responses = await self.data_manager.extract_data_chronobid(project_id, self.criteria)

                if not responses['ids'] and len(responses['metadatas']) == 0:
                    raise RuntimeError("Unable to find Source document")
                
                print('DATA SET 1 responses: ', responses)
                pinecone_data = []
                chunk_detailed_content = []
                source_document_text = ""
                for resp in range(len(responses['metadatas'])):
                    metadata = responses['metadatas'][resp]

                    pinecone_data.append(metadata.get("documents", ""))
                    detailed_chunk = metadata.get("detailed_chunk", "")
                    
                    if detailed_chunk:
                        chunk_detailed_content.append(detailed_chunk)
                    else:
                        source_document_text += metadata.get("documents", "") + "\n"
                
                if source_document_text != "":
                    print('comes in whole..')
                    return source_document_text
                else:
                    source_document_text = await self.process_source_file_Chronobid(chunk_detailed_content, self.criteria, count)
                    print('comes in chunk..')
                    return source_document_text
            except Exception as e:
                attempt += 1
                print(f'Attempt {attempt} failed to get Dataset1: {e}')
                if attempt == retries:
                    print('All attempts failed. Returning error.')
                    return None
    
    def get_dataset1_sync(self, project_id, count):
        attempt = 0
        retries=3
        while attempt < retries:
            try:
                print('i enter dataset1 class')
                # Here we extract data sourcexx1 from Pinecone based on criteria
                responses = self.data_manager.extract_data_chronobid_sync(project_id, self.criteria)

                if not responses['ids'] and len(responses['metadatas']) == 0:
                    raise RuntimeError("Unable to find Source document")
                
                pinecone_data = []
                chunk_detailed_content = []
                source_document_text = ""
                for resp in range(len(responses['metadatas'])):
                    metadata = responses['metadatas'][resp]

                    pinecone_data.append(metadata.get("documents", ""))
                    detailed_chunk = metadata.get("detailed_chunk", "")
                    if detailed_chunk:
                        chunk_detailed_content.append(detailed_chunk)
                    else:
                        source_document_text += metadata.get("documents", "") + "\n"
                
                if source_document_text != "":
                    print('comes in whole..')
                    return source_document_text
                else:
                    source_document_text = self.process_source_file_Chronobid_sync(chunk_detailed_content, self.criteria, count)
                    print('comes in chunk..')
                    return source_document_text
            except Exception as e:
                attempt += 1
                print(f'Attempt {attempt} failed to get Dataset1: {e}')
                if attempt == retries:
                    print('All attempts failed. Returning error.')
                    return None
    

    def get_dataset1_sync_latest(self, project_id, count, sub_criteria=None):
        """
        Retrieve source documents based on main criteria and optional sub-criteria
        
        Args:
            project_id: The project identifier
            count: Number of results to retrieve
            sub_criteria: List of additional criteria to process separately
            
        Returns:
            dict: Contains 'main_document' and 'sub_documents' (list of documents for sub-criteria)
        """
        # Initialize return structure
        result = {
            'main_document': None,
            'sub_documents': []
        }
        
        # First get the main document using the primary criteria
        result['main_document'] = self._get_single_document_v2(project_id, count, self.criteria)
        
        # If sub_criteria is provided, get documents for each sub-criterion
        if sub_criteria and isinstance(sub_criteria, list):
            for criterion in sub_criteria:
                sub_doc = self._get_single_document_v2(project_id, count, criterion)
                if sub_doc:
                    result['sub_documents'].append(sub_doc)
        
        return result
    
    def _get_single_document(self, project_id, count, criteria):
        """
        Helper method to get a single document based on given criteria
        """
        attempt = 0
        retries = 3
        while attempt < retries:
            try:
                print(f'Retrieving data for criteria: {criteria}')
                # Extract data from Pinecone based on criteria
                responses = self.data_manager.extract_data_chronobid_sync(project_id, criteria)
                #save as json
                
                if not responses['ids'] and len(responses['metadatas']) == 0:
                    print(f"Unable to find Source document for criteria: {criteria}")
                    return None
                
                print(f"\033[93mDATASET FROM PINECONE FOR DATASET 1: {responses}\033[0m")

                pinecone_data = []
                chunk_detailed_content = []
                source_document_text = ""
                for resp in range(len(responses['metadatas'])):
                    metadata = responses['metadatas'][resp]

                    pinecone_data.append(metadata.get("documents", ""))
                    document_name = metadata.get("source", "")
                    detailed_chunk = metadata.get("detailed_chunk", "")
                    if detailed_chunk:
                        chunk_detailed_content.append(detailed_chunk)
                        source_document_text += f"\n\n==== SOURCE_DOCMENT_NAME: {document_name} ====\n"
                    else:
                        source_document_text += metadata.get("documents", "") + "\n"
                        source_document_text += f"\n\n==== SOURCE_DOCMENT_NAME: {document_name} ====\n"

                if source_document_text != "":
                    print('Processing complete document...')
                    return source_document_text
                else:
                    source_document_text = self.process_source_file_Chronobid_sync(chunk_detailed_content, criteria, count)
                    print('Processing document chunks...')
                    return source_document_text
            except Exception as e:
                attempt += 1
                print(f'Attempt {attempt} failed to get document: {e}')
                if attempt == retries:
                    print('All attempts failed. Returning None.')
                    return None
                
    def _get_single_document_v2(self, project_id, count, criteria):
        """
        Helper method to get a single document based on given criteria
        """
        attempt = 0
        retries = 1
        while attempt < retries:
            try:
                import re
                print(f'Retrieving data for criteria: {criteria}')

                synonyms = self.synonym_service.generate_synonym_sync(f"{criteria['name']}\n Description: {criteria['description']}")

        # Combine all matches into a single string separated by '; '
                similarQuestion = [f"{name.strip()} - {description.strip()}"
                for name, description in re.findall(r"Name:\s*(.+?)\n\s*Description:\s*(.+?)(?:\n|</SYNONYMS>)", synonyms, re.DOTALL)][:2]
                # Extract data from Pinecone based on criteria
                query = f"{criteria['name']} {criteria['description']}"
                responses = self.data_manager.extract_data_v2_sync(project_id,[query], similarQuestion, 45)

                sources =responses[0]['source_list']

                return sources
                #save as json
               
            except Exception as e:
                attempt += 1
                print(f'Attempt {attempt} failed to get document: {e}')
                if attempt == retries:
                    print('All attempts failed. Returning None.')
                    return None
            
    async def process_source_file_Chronobid(self, chunks_array, criteria, count):
        
        synonyms = await self.synonym_service.generate_synonym(f"{criteria['name']}\n Description: {criteria['description']}")
        query = f"""

            Question {count} 
            Title: {criteria['name']}
            Description: {criteria['description']}

            {synonyms}
            
        """
        # print('Query:', query)
        # print('Chunks array:', chunks_array)
        print('Query from source document CBP...')

        self.cohere_embedding = CohereService()
        # retrieved_texts = self.cohere_embedding.search_similar(query, chunks_array)
        reranked_texts = self.cohere_embedding.rerank_texts(query, chunks_array)
        retrieved_texts = reranked_texts
            
        return retrieved_texts
    
    def process_source_file_Chronobid_sync(self, chunks_array, criteria, count):
        
        
        synonyms = self.synonym_service.generate_synonym_sync(f"{criteria['name']}\n Description: {criteria['description']}")
        query = f"""

            Question {count} 
            Title: {criteria['name']}
            Description: {criteria['description']}

            {synonyms}
            
        """
        # print('Query:', query)
        # print('Chunks array:', chunks_array)
        print('Query from source document CBP...')

        self.cohere_embedding = CohereService()
        # retrieved_texts = self.cohere_embedding.search_similar(query, chunks_array)
        reranked_texts = self.cohere_embedding.rerank_texts(query, chunks_array)
        retrieved_texts = reranked_texts
            
        return retrieved_texts
    
    def extract_tender_relevnt_chunks(self, project_id, criteria,count=None):
        """
        Helper method to get a single document based on given criteria
        """
        attempt = 0
        retries = 1
        while attempt < retries:
            try:
                import re
                print(f'Retrieving data for criteria: {criteria}')

                synonyms = self.synonym_service.generate_synonym_sync(f"{criteria['name']}\n Description: {criteria['description']}")

        # Combine all matches into a single string separated by '; '
                similarQuestion = [f"{name.strip()} - {description.strip()}"
                for name, description in re.findall(r"Name:\s*(.+?)\n\s*Description:\s*(.+?)(?:\n|</SYNONYMS>)", synonyms, re.DOTALL)][:2]
                # Extract data from Pinecone based on criteria
                query = f"{criteria['name']} {criteria['description']}"
                sources = self.data_manager.extract_data_v2_sync(project_id,[query], similarQuestion, 20)

                
                

                sources =sources[0]['source_list']


                sources = [{
            'section_id': source.get('section_id', ''), 
            'source': source.get('source', ''), 
            'page_number': source.get('page_number', ''),
            'content': source.get('content', ''),
            # 'matched_content': source.get('matched_content', ''),
            'title': source.get('title', ''),
            } for source in sources]


                # import json
                # with open(f"criteria_docs_{project_id}.json", "a") as f:
                #     json.dump({
                #         "criteria_name": criteria.get('name'),
                #         "criteria_chunk_tender": sources
                #     }, f)
                #     f.write("\n")

                result = self.chronobid_reasoning.generate_tender_docs_for_criteria(
                    criteria,
                    sources,
                    count
                )

                return result
                #save as json
               
            except Exception as e:
                attempt += 1
                print(f'Attempt {attempt} failed to get document: {e}')
                if attempt == retries:
                    print('All attempts failed. Returning None.')
                    return None


async def get_dataset1():
    criteria = {"name": "Foriegn Investments Prohibition", "description": "Where is foreign investments prohibited", "weight": "100"}
    extractor = Dataset1Extractor(criteria)
    return await extractor.get_dataset1('2b6849da-34e1-4bc3-a47b-76a87831803c', 1, {"input_token": 0, "output_token": 0})

# New function to run the async method
def run_get_dataset1():
    result = asyncio.run(get_dataset1())  # Run the async function
    print(result)  # Print the result

if __name__ == "__main__":
    
    project_id = "1cb01754-1998-4c81-8b9e-d3389a8d197d"
    criteria = {"name":"Price","description":"Cost of equipment and all necessary components, including spare parts (commissioning, 1 year of operation, capital). The bidder must inform the prices considered for the equipment and the spare parts. All line items must be fully priced. Spares pricing must be in accordance with the Project Requirements."}

    data_extractor = Dataset1Extractor(criteria)
    result = data_extractor.extract_tender_relevnt_chunks(project_id,criteria)
    print(result)
    # Call the function to execute
    #run_get_dataset1()

  