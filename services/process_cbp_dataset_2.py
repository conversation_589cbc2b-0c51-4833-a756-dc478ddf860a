# FILE 1: Dataset2Extractor Class and Related Functions
import gevent
from gevent import monkey
monkey.patch_all()
import json
import gevent
import re
import time
import asyncio
import os, sys
from services.parser_openai_parellel import OpenAIProcessor
from services.pull_reference import find_reference
from services.summarization import SummarizationAssistant
from services.parser_openai import AIProcessor
from services.synonym_expansion import SynonymGenerator
from services.process_cbp_evaluate_chunks import AIChunckProcessor
from services.cohere_embedding import CohereService
from services.engr_doc_types_detection import EngrDocTypesDetection
from services.data_handler import DataManager
from concurrent.futures import ThreadPoolExecutor
from services.faiss_embedding import FaissEmbedding
from typing import Dict, List, Tuple, Any, Optional

class Dataset2Extractor:
    def __init__(self, socket_manager):
        self.open_ai_processor = OpenAIProcessor()
        self.summarization = SummarizationAssistant()
        self.find_reference = find_reference
        self.parser_openai = AIProcessor()
        self.synonym_service = SynonymGenerator()
        self.chunck_processor = AIChunckProcessor()
        self.engr_doc_detector = EngrDocTypesDetection()
        self.data_manager = DataManager()
        self.faiss_processor = FaissEmbedding()
        self.socket_manager = socket_manager
        self.requirement_id = ""
        self.type = "claude"

    async def process_new_file_Chronobid(self, criteria, source_document_text, metadata, count, log):
        self.requirement_metadata = metadata
        # print(f"Entering process new chronobid file {base_file_path}")
        t1 = time.time()
        print(f"\033[92mquery : {criteria}\033[0m")  # Print in green color
        
        self.add_event(self.requirement_id, 'progress_message', {
            'event_type': 'CBP',
            'request_id': self.requirement_id,
            'data': {'status': 'processing', 'message': f"Starting evaluation for Criteria {count}.", 'query_idx': count}
        })

        query = f"Question {count}\nTitle: {criteria['name']}\nDescription: {criteria['description']}"

        print('now trying to do the search....')
        print(self.requirement_metadata.get('exist_bid', False))
        if self.requirement_metadata.get('exist_bid') and self.requirement_metadata.get('bid_id'):
            close_upload_text = await self.faiss_processor.search([query], self.requirement_metadata.get('bid_id'), 13)
        else:
            close_upload_text = await self.faiss_processor.search([query], log['requirement_id'], 12)

        print('this is uploaded doc text:', close_upload_text)

        
        start_time = time.time()
        synonyms = await self.synonym_service.generate_synonym(f"{criteria['name']}\n Description: {criteria['description']}")
        generate_synonym_time = time.time() - start_time
        print(f"Time for generate_synonym: {generate_synonym_time:.2f} seconds")
        synonyms_name_descriptions = [f"{name.strip()} - {description.strip()}" for name, description in re.findall(r"Name:\s*(.+?)\n\s*Description:\s*(.+?)(?:\n|</SYNONYMS>)", synonyms, re.DOTALL)]

        print('this is synonym desc: ', synonyms_name_descriptions)
        print('this is bid id: ', self.requirement_metadata.get('bid_id'))
        print('this is exist bid: ', self.requirement_metadata.get('exist_bid'))
        if self.requirement_metadata.get('exist_bid') and self.requirement_metadata.get('bid_id'):
            close_synonyms = await self.faiss_processor.search(synonyms_name_descriptions, self.requirement_metadata.get('bid_id'), 9)
        else:
            close_synonyms = await self.faiss_processor.search(synonyms_name_descriptions, log['requirement_id'], 8)

        print('this is close synonyms: ', close_synonyms)
        
        # join close_upload_text and close_synonyms
        '''
        closed_upload_text was created like this, join them together
        query_matches = [
                                    (item["content"], item["score"])
                                    for item in query_results
                                ]
        close_upload_text = [ query_matches ]
        '''
        
        close_upload_text = [
            ("\n".join([item[0] for item in close_upload_text[0]]), 0)
        ]

        t3 = time.time()
        evaluate_data = await self.master_evaluation_v3(source_document_text, close_upload_text, close_synonyms, criteria, count, 3)
        t4 = time.time()
        print(f"master evaluation for criteria {count} done in {t4 - t3:.2f} seconds\n")
        print(f'this is evaluation for criteria {count} data: ', evaluate_data)
        

        # resetting the array values
        log['evaluation_report_section']['strength_chunk'][count -1] = []
        log['evaluation_report_section']['weak_chunk'][count -1] = []
        log['evaluation_report_section']['risk_chunk'][count -1] = []
        
        for section in evaluate_data:
            # print('this is section: ', section)

            # Check if section is a dictionary or a list
            if isinstance(section, dict):
                # Process as dictionary
                try:
                    # section['evaluation_data'] = section['evaluation_data'].__dict__
                    score = int(section['evaluation_data']['Score'])
                    print('this is score....', score)
                    print('section type: ', type(section))

                    if 20 < score < 81:
                        print('if.....')
                        log['evaluation_report_section']['weak_chunk'][count -1].append(section)
                    elif score >= 81:
                        print('elif....')
                        log['evaluation_report_section']['strength_chunk'][count -1].append(section)
                    else:
                        print('else.....')
                        log['evaluation_report_section']['risk_chunk'][count -1].append(section)
                except (KeyError, ValueError, IndexError) as e:
                    print(f"Error processing dictionary section: {e}")
            elif isinstance(section, list):
                # Process as list (assuming all items in the list are valid dictionary sections)
                for item in section:
                    try:
                        # item['evaluation_data'] = item['evaluation_data'].__dict__
                        score = int(item['evaluation_data']['Score'])
                        print('this is score....', score)
                        print('item type: ', type(item))

                        if 20 < score < 81:
                            print('if.....')
                            log['evaluation_report_section']['weak_chunk'][count -1].append(item)
                        elif score >= 81:
                            print('elif....')
                            log['evaluation_report_section']['strength_chunk'][count -1].append(item)
                        else:
                            print('else.....')
                            log['evaluation_report_section']['risk_chunk'][count -1].append(item)
                    except (KeyError, ValueError, IndexError) as e:
                        print(f"Error processing list item: {e}")
            else:
                print("Unknown type for section:", type(section))
        
        # Add an event for evaluation completion
        self.add_event(self.requirement_id, 'progress_message', {
            'event_type': 'CBP',
            'request_id': self.requirement_id,
            'data': {'status': 'completed', 'message': f"Evaluation completed for Query {count}.", 'query_idx': count}
        })


        print(f"Done with sections evaluation CBP for criteria {count}....")
        return evaluate_data
    
    # async def process_files_parallel(self, file_names, base_file_path):
    #     data = []
    #     tasks = []
    #     for file_path in file_names:
    #         task = self.open_ai_processor.get_serialized_data_openai_parser(os.path.join(base_file_path, file_path))
    #         tasks.append(task)
    #     results = await asyncio.gather(*tasks)
        
    #     # Append the second part of each result to the 'data' list
    #     for result in results:
    #         data.extend(result[1])  # Assuming the second element is what we want
    #     return data


    # async def search_chunks_parallel(self, query_title_desc, chunked_text, batch_size=50):
    #     results = []

    #     batch_size = min(batch_size, len(chunked_text))
        
    #     # Helper function for batch processing
    #     def search_batch(batch):
    #         return self.cohere_embedding.search_similar(query_title_desc, batch)
        
    #     # Batch chunks
    #     with ThreadPoolExecutor() as executor:
    #         for i in range(0, len(chunked_text), batch_size):
    #             batch = chunked_text[i:i + batch_size]
    #             futures = [executor.submit(search_batch, batch)]
    #             for future in futures:
    #                 results.extend(future.result())

    #     return results

    def process_new_file_Chronobid_sync(self, criteria, source_document_text, metadata, count, log):
        self.requirement_metadata = metadata
        # print(f"Entering process new chronobid file {base_file_path}")
        t1 = time.time()
        print(f"\033[92mquery : {criteria}\033[0m")  # Print in green color
        
        self.add_event(self.requirement_id, 'progress_message', {
            'event_type': 'CBP',
            'request_id': self.requirement_id,
            'data': {'status': 'processing', 'message': f"Starting evaluation for Criteria {count}.", 'query_idx': count}
        })

        query = f"Question {count}\nTitle: {criteria['name']}\nDescription: {criteria['description']}"

        print('\033[92mRequirement metadata:', self.requirement_metadata, '\033[0m')
        print('\033[92mPage:', query, '\033[0m')
        print('now trying to do the search....')
        print(self.requirement_metadata.get('exist_bid', False))
        if self.requirement_metadata.get('exist_bid') and self.requirement_metadata.get('bid_id'):
            close_upload_text = self.faiss_processor.search_sync([query], self.requirement_metadata.get('bid_id'), 13)
        else:
            close_upload_text = self.faiss_processor.search_sync([query], log['requirement_id'], 12)

        # print('this is uploaded doc text:', close_upload_text)

        
        start_time = time.time()
        synonyms = self.synonym_service.generate_synonym_sync(f"{criteria['name']}\n Description: {criteria['description']}")
        generate_synonym_time = time.time() - start_time
        print(f"Time for generate_synonym: {generate_synonym_time:.2f} seconds")
        synonyms_name_descriptions = [f"{name.strip()} - {description.strip()}" for name, description in re.findall(r"Name:\s*(.+?)\n\s*Description:\s*(.+?)(?:\n|</SYNONYMS>)", synonyms, re.DOTALL)]

        # print('this is synonym desc: ', synonyms_name_descriptions)
        # print('this is bid id: ', self.requirement_metadata.get('bid_id'))
        # print('this is exist bid: ', self.requirement_metadata.get('exist_bid'))
        if self.requirement_metadata.get('exist_bid') and self.requirement_metadata.get('bid_id'):
            close_synonyms = self.faiss_processor.search_sync(synonyms_name_descriptions, self.requirement_metadata.get('bid_id'), 9)
        else:
            close_synonyms = self.faiss_processor.search_sync(synonyms_name_descriptions, log['requirement_id'], 8)

        # print('this is close synonyms: ', close_synonyms)
        
        # join close_upload_text and close_synonyms
        '''
        closed_upload_text was created like this, join them together
        query_matches = [
                                    (item["content"], item["score"])
                                    for item in query_results
                                ]
        close_upload_text = [ query_matches ]
        '''
        
        close_upload_text = [
            ("\n".join([item[0] for item in close_upload_text[0]]), 0)
        ]

        t3 = time.time()
        evaluate_data = self.master_evaluation_v3_sync(source_document_text, close_upload_text, close_synonyms, criteria, count, 3)
        t4 = time.time()
        print(f"master evaluation for criteria {count} done in {t4 - t3:.2f} seconds\n")
        # print(f'this is evaluation for criteria {count} data: ', evaluate_data)
        

        # resetting the array values
        log['evaluation_report_section']['strength_chunk'][count -1] = []
        log['evaluation_report_section']['weak_chunk'][count -1] = []
        log['evaluation_report_section']['risk_chunk'][count -1] = []
        
        for section in evaluate_data:
            # print('this is section: ', section)

            # Check if section is a dictionary or a list
            if isinstance(section, dict):
                # Process as dictionary
                try:
                    # section['evaluation_data'] = section['evaluation_data'].__dict__
                    score = int(section['evaluation_data']['Score'])
                    print('this is score....', score)
                    print('section type: ', type(section))

                    if 20 < score < 81:
                        print('if.....')
                        log['evaluation_report_section']['weak_chunk'][count -1].append(section)
                    elif score >= 81:
                        print('elif....')
                        log['evaluation_report_section']['strength_chunk'][count -1].append(section)
                    else:
                        print('else.....')
                        log['evaluation_report_section']['risk_chunk'][count -1].append(section)
                except (KeyError, ValueError, IndexError) as e:
                    print(f"Error processing dictionary section: {e}")
            elif isinstance(section, list):
                # Process as list (assuming all items in the list are valid dictionary sections)
                for item in section:
                    try:
                        # item['evaluation_data'] = item['evaluation_data'].__dict__
                        score = int(item['evaluation_data']['Score'])
                        print('this is score....', score)
                        print('item type: ', type(item))

                        if 20 < score < 81:
                            print('if.....')
                            log['evaluation_report_section']['weak_chunk'][count -1].append(item)
                        elif score >= 81:
                            print('elif....')
                            log['evaluation_report_section']['strength_chunk'][count -1].append(item)
                        else:
                            print('else.....')
                            log['evaluation_report_section']['risk_chunk'][count -1].append(item)
                    except (KeyError, ValueError, IndexError) as e:
                        print(f"Error processing list item: {e}")
            else:
                print("Unknown type for section:", type(section))
        
        # Add an event for evaluation completion
        self.add_event(self.requirement_id, 'progress_message', {
            'event_type': 'CBP',
            'request_id': self.requirement_id,
            'data': {'status': 'completed', 'message': f"Evaluation completed for Criteria {count}.", 'query_idx': count}
        })


        print(f"Done with sections evaluation CBP for criteria {count}....")
        return evaluate_data
    
  
    def process_hierarchical_criteria_evaluation(self, criteria_data, metadata,count,log,bid_id):
        """
        Process main criteria and sub-criteria with their respective source documents
        
        Args:
            criteria_data: Dictionary containing:
                - 'main_criteria': Main criteria object
                - 'main_document': Source document for main criteria
                - 'sub_criteria': List of sub-criteria objects
                - 'sub_documents': List of source documents for sub-criteria
            metadata: Metadata about the requirement
            log: Log object for tracking progress and storing results
            
        Returns:
            dict: Contains evaluation results for main criteria and sub-criteria
        """

        self.requirement_metadata = metadata
        t1 = time.time()
        
        # Initialize the log structure if needed to include main and sub criteria sections
        if 'main_criteria_evaluation' not in log:
            log['main_criteria_evaluation'] = {
                'strength_chunk': [],
                'weak_chunk': [],
                'risk_chunk': []
            }
        
        if 'sub_criteria_evaluations' not in log:
            log['sub_criteria_evaluations'] = []
        
        results = {
            
            'main_evaluation': None,
            'sub_evaluations': []
        }
        # Process main criteria
        main_criteria = criteria_data['main_criteria']
        main_document = criteria_data['main_document']
        main_count = 1  # Start with count 1 for main criteria
        
        # Create a temporary log for processing the main criteria
        main_temp_log = {
            'requirement_id': log['requirement_id'],
            'evaluation_report_section': {
                'strength_chunk': [[]],
                'weak_chunk': [[]],
                'risk_chunk': [[]]
            }
        }
        # Process the main criteria
        print(f"\033[92mProcessing main criteria: {main_criteria['name']}\033[0m")
        results['main_evaluation'] = self._process_single_criteria(
            main_criteria, 
            main_document, 
            main_count, 
            main_temp_log,
            bid_id
        )
        # Process sub-criteria if they exist
        if 'sub_criteria' in criteria_data and 'sub_documents' in criteria_data:
            sub_criteria_list = criteria_data['sub_criteria']
            sub_documents = criteria_data['sub_documents']
            
            # Ensure both lists are of the same length
            if len(sub_criteria_list) != len(sub_documents):
                print(f"Warning: Number of sub-criteria ({len(sub_criteria_list)}) " 
                    f"doesn't match number of documents ({len(sub_documents)})")
                # Process only the minimum number available
                process_count = min(len(sub_criteria_list), len(sub_documents))
            else:
                process_count = len(sub_criteria_list)
            
            # Process each sub-criteria with its corresponding document
            for i in range(process_count):
                sub_count = i + 1  # Count for sub-criteria (1-based)
                sub_criteria = sub_criteria_list[i]
                sub_document = sub_documents[i]
                
                # Create a temporary log for this sub-criteria
                sub_temp_log = {
                    'requirement_id': log['requirement_id'],
                    'evaluation_report_section': {
                        'strength_chunk': [[]],
                        'weak_chunk': [[]],
                        'risk_chunk': [[]]
                    }
                }
                
                print(f"\033[92mProcessing sub-criteria {sub_count}: {sub_criteria['name']}\033[0m")
                sub_evaluation= self._process_single_criteria(
                    sub_criteria,
                    sub_document,
                    sub_count,  # Use sub_count for the internal processing
                    sub_temp_log,
                    bid_id
                )
                
                # Create a sub-criteria result entry
                sub_result = {
                    'criteria': sub_criteria,
                    'evaluate_summary_processing': sub_evaluation,
                    
                }
                # Add to results and log
                results['sub_evaluations'].append(sub_result)
            
        print(f"All criteria processing completed in {time.time() - t1:.2f} seconds")
        return results

    
   

    def generate_evaluation_texts(self, results_main: Dict[str, Any], source_text: Dict[str, Any]) -> Tuple[str, List[str]]:
        """
        Generate formatted evaluation texts from processing results.

        Args:
            results_main (Dict[str, Any]): The results from processing the main and sub criteria.
            source_text (Dict[str, Any]): The text content extracted for main and sub criteria.

        Returns:
            Tuple[str, List[str]]: 
                - main_criteria_text: Formatted HTML for main criteria
                - sub_criteria_texts: List of formatted HTMLs for each sub-criteria
        """

        # === MAIN CRITERIA PROCESSING ===
        main_evaluation: Dict[str, Any] = results_main.get("main_evaluation", {})
        main_reference_text: str = source_text.get("main_document", "") or ""
        main_criteria_text: str = "<CRITERIA>Main Criteria</CRITERIA><br/>"

        for category in ['strength_chunk', 'weak_chunk', 'risk_chunk']:
            category_chunks: List[List[Any]] = main_evaluation.get(category, [[]])
            for chunk_list in category_chunks:
                for evaluation_item in chunk_list:
                    if isinstance(evaluation_item, dict) and 'evaluation_data' in evaluation_item:
                        evaluation_data: Dict[str, Any] = evaluation_item['evaluation_data']
                        main_criteria_text += f"""
                            <Evaluation>{evaluation_data.get('Evaluation', '')}</Evaluation><br/>
                            <Score>{evaluation_data.get('Score', '')}</Score><br/>
                            <Reason>{evaluation_data.get('Reason', '')}</Reason><br/>
                            <Reference>{main_reference_text}</Reference><br/>
                        """

        # === SUB-CRITERIA PROCESSING ===
        sub_criteria_texts: List[str] = []
        sub_evaluations: List[Dict[str, Any]] = results_main.get("sub_evaluations", [])
        sub_sources: List[str] = source_text.get("sub_documents", [])

        for sub_index, sub_evaluation in enumerate(sub_evaluations):
            sub_number: int = sub_index + 1
            sub_criteria_name: str = sub_evaluation.get('criteria', {}).get('name', '')
            sub_reference_text: str = sub_sources[sub_index] if sub_index < len(sub_sources) else ""

            sub_criteria_text: str = f"<CRITERIA>Sub-Criteria {sub_number}: {sub_criteria_name}</CRITERIA><br/>"
            sub_evaluation_data: Dict[str, Any] = sub_evaluation.get('evaluate_summary_processing', {})

            for category in ['strength_chunk', 'weak_chunk', 'risk_chunk']:
                category_chunks: List[List[Any]] = sub_evaluation_data.get(category, [[]])
                for chunk_list in category_chunks:
                    for evaluation_item in chunk_list:
                        if isinstance(evaluation_item, dict) and 'evaluation_data' in evaluation_item:
                            item_data: Dict[str, Any] = evaluation_item['evaluation_data']
                            sub_criteria_text += f"""
                                <Evaluation>{item_data.get('Evaluation', '')}</Evaluation><br/>
                                <Score>{item_data.get('Score', '')}</Score><br/>
                                <Reason>{item_data.get('Reason', '')}</Reason><br/>
                                <Reference>{sub_reference_text}</Reference><br/>
                            """

            sub_criteria_texts.append(sub_criteria_text)

        return main_criteria_text, sub_criteria_texts

    
    
    def _process_single_criteria(self, criteria, source_document_text, count, log,bid_id):
        """
        Helper method to process a single criteria with its source document
        """
        print(f"\033[92mquery : {criteria}\033[0m")  # Print in green color
        
        self.add_event(self.requirement_id, 'progress_message', {
            'event_type': 'CBP',
            'request_id': self.requirement_id,
            'data': {'status': 'processing', 'message': f"Starting evaluation for Criteria {count}.", 'query_idx': count}
        })

        query = f"Question {count}\nTitle: {criteria['name']}\nDescription: {criteria['description']}"
        
        print('\033[92mRequirement metadata:', self.requirement_metadata, '\033[0m')
        print('\033[92mPage:', query, '\033[0m')
        print('now trying to do the search....')
        
        # Search for relevant text
        query = f"Criteria Name: {criteria['name']}\n Criteria Description: {criteria['description']}"

        #expanded_query = json.loads(self.expand_criteria_query(query))["query_variations"]
        
        synos = self.synonym_service.generate_synonym_sync(f"{criteria['name']}\n Description: {criteria['description']}")
        similarQuestion = [f"{name.strip()} - {description.strip()}"
                        for name, description in re.findall(r"Name:\s*(.+?)\n\s*Description:\s*(.+?)(?:\n|</SYNONYMS>)", synos, re.DOTALL)][:2]
        #query = f"Citeria Name : {criteria['name']}\n Description: {criteria['description']}"
        query = f"Question {count}\nTitle: {criteria['name']}\nDescription: {criteria['description']}"
        
        search_queries = [query] + similarQuestion
        
        if self.requirement_metadata.get('exist_bid') and self.requirement_metadata.get('bid_id'):
            print(f"NOW TRYING TO SEARCH GPU SERVER: {search_queries[0]}")
            close_upload_text = self.faiss_processor.search_sync(search_queries, self.requirement_metadata.get('bid_id'), 80)
            print("THIS IS THE DATA FROM GPY SERVER: ", close_upload_text)
        # else:
        #     close_upload_text = self.faiss_processor.search_sync([query], log['requirement_id'], 12)
        #____ Read existing data if file exists


        """
        existing_data = []
        try:
            with open(f"criteria_docs_{bid_id}.json", "r") as f:
                for line in f:
                    if line.strip():  # Skip empty lines
                        existing_data.append(json.loads(line))
        except FileNotFoundError:
            pass

        # Append new data
        existing_data.append({
            "criteria_name": criteria.get('name'),
            "criteria_chunk_bid": close_upload_text
        })

        # Write all data back to file
        with open(f"criteria_docs_{bid_id}.json", "w") as f:
            for item in existing_data:
                json.dump(item, f)
                f.write("\n")
            """  
        #-----
        '''
        # Generate synonyms
        start_time = time.time()
        synonyms = self.synonym_service.generate_synonym_sync(f"{criteria['name']}\n Description: {criteria['description']}")
        generate_synonym_time = time.time() - start_time
        print(f"Time for generate_synonym: {generate_synonym_time:.2f} seconds")
        
        synonyms_name_descriptions = [f"{name.strip()} - {description.strip()}" for name, description in re.findall(r"Name:\s*(.+?)\n\s*Description:\s*(.+?)(?:\n|</SYNONYMS>)", synonyms, re.DOTALL)]
        

        # Search for synonyms
        if self.requirement_metadata.get('exist_bid') and self.requirement_metadata.get('bid_id'):
            close_synonyms = self.faiss_processor.search_sync(synonyms_name_descriptions, self.requirement_metadata.get('bid_id'), 20)
        else:
            close_synonyms = self.faiss_processor.search_sync(synonyms_name_descriptions, log['requirement_id'], 8)'''
        
        # Format close_upload_text
        #close_upload_text = [
          #  ("\n".join([item[0] for item in close_upload_text[0]]), 0)
        #]

        # Run master evaluation
        t3 = time.time()
        close_synonyms = None
        evaluate_data = self.master_evaluation_v3_sync(source_document_text, close_upload_text, close_synonyms, criteria, count, 3)
        t4 = time.time()
        print(f"master evaluation for criteria {count} done in {t4 - t3:.2f} seconds\n")
        
        # Ensure there's at least one index in the arrays
        if not log['evaluation_report_section']['strength_chunk']:
            log['evaluation_report_section']['strength_chunk'].append([])
            log['evaluation_report_section']['weak_chunk'].append([])
            log['evaluation_report_section']['risk_chunk'].append([])
        
        # Reset arrays for this criteria (always using index 0 since we're using temporary logs)
        log['evaluation_report_section']['strength_chunk'][0] = []
        log['evaluation_report_section']['weak_chunk'][0] = []
        log['evaluation_report_section']['risk_chunk'][0] = []
        
        # Process evaluation results
        for section in evaluate_data:
            # Check if section is a dictionary or a list
            if isinstance(section, dict):
                # Process as dictionary
                try:
                    score = int(section['evaluation_data']['Score'])
                    print('this is score....', score)
                    print('section type: ', type(section))

                    if 20 < score < 81:
                        print('if.....')
                        log['evaluation_report_section']['weak_chunk'][0].append(section)
                    elif score >= 81:
                        print('elif....')
                        log['evaluation_report_section']['strength_chunk'][0].append(section)
                    else:
                        print('else.....')
                        log['evaluation_report_section']['risk_chunk'][0].append(section)
                except (KeyError, ValueError, IndexError) as e:
                    print(f"Error processing dictionary section: {e}")

            elif isinstance(section, list):
                # Process as list (assuming all items in the list are valid dictionary sections)
                for item in section:
                    try:
                        score = int(item['evaluation_data']['Score'])
                        print('this is score....', score)
                        print('item type: ', type(item))

                        if 20 < score < 81:
                            print('if.....')
                            log['evaluation_report_section']['weak_chunk'][0].append(item)
                        elif score >= 81:
                            print('elif....')
                            log['evaluation_report_section']['strength_chunk'][0].append(item)
                        else:
                            print('else.....')
                            log['evaluation_report_section']['risk_chunk'][0].append(item)
                    except (KeyError, ValueError, IndexError) as e:
                        print(f"Error processing list item: {e}")
            else:
                print("Unknown type for section:", type(section))
        
        # Add an event for evaluation completion
        self.add_event(self.requirement_id, 'progress_message', {
            'event_type': 'CBP',
            'request_id': self.requirement_id,
            'data': {'status': 'completed', 'message': f"Evaluation completed for Criteria {count}.", 'query_idx': count}
        })

        print(f"Done with sections evaluation CBP for criteria {count}....")
        return log["evaluation_report_section"]
    

    def expand_criteria_query(self,criteria_query, temperature=0.001):
        from services.prompt_loader import PromptLoader
        from services.claude_ai_service import ClaudeService

        prompt = PromptLoader().get_prompt('criteria_query_expansion', {"criteria_input": criteria_query})
        messages = [
            {
                "role": "user",
                "content":  prompt
            }
        ]
        claude_client = ClaudeService()
        response = claude_client.generate_message_agent_sonnet_new_sync(messages=messages, temperature=temperature, max_tokens=4096)
        return response.content[0].text
    

    async def master_evaluation_v3(self, source_document_text, uploaded_text, synonyms_text, criteria, count, limit=3):
        if uploaded_text:
            # Extracting documents and sorting by score
            high_score_texts_sorted = sorted(uploaded_text, key=lambda x: x[1], reverse=True)  # x[1] is the score
            # print('top high scores text: ', high_score_texts_sorted)

            async def process_texts_concurrently(high_texts):
                tasks = [
                    self.chunck_processor.get_claude_to_evaluate_chunks_based_on_criteria(
                        high_text[0], source_document_text, criteria, count  # high_text[0] is the Document
                    ) for high_text in high_texts
                ]
                
                results = await asyncio.gather(*tasks, return_exceptions=True)
                unified_responses = []
                for text, result in zip(high_texts, results):
                    if isinstance(result, Exception):
                        print(f"Error processing high texts: {result}")
                        continue
                    json_result = result
                    unified_responses.append({"text_content": text[0], "evaluation_data": json_result})  # text[0] is the Document
                    
                return unified_responses

            high_text_responses = await process_texts_concurrently(high_score_texts_sorted)
    
            self.add_event(self.requirement_id , 'progress_message', {
                'event_type': 'CBP',
                'request_id': self.requirement_id,
                'data': {'status': 'completed', 'message': f"Evaluation completed for query {count}.", 'query_idx': count}
            })

            return high_text_responses
        
        # If synonyms have high similarity, process them
        if synonyms_text:
            # Sort the close score synonyms by the highest score in descending order
            # close_synonyms_sorted = sorted(synonyms_text, key=lambda x: x[1], reverse=True)  # x[1] is the score
            all_results = []
            for sublist in synonyms_text:
                all_results.extend(sublist)

            close_synonyms_sorted = sorted(all_results, key=lambda x: x[1], reverse=True)[:5]
            print('synonyms top 5: ', close_synonyms_sorted)

            async def process_synonyms_concurrently(synonyms):
                tasks = [
                    self.chunck_processor.get_claude_to_evaluate_chunks_based_on_criteria(
                        synonym[0], source_document_text, criteria, count  # synonym[0] is the Document
                    ) for synonym in synonyms
                ]
                
                results = await asyncio.gather(*tasks, return_exceptions=True)
                unified_responses = []
                for text, result in zip(synonyms, results):
                    if isinstance(result, Exception):
                        print(f"Error processing synonym: {result}")
                        continue
                    json_result = result
                    unified_responses.append({"text_content": text[0], "evaluation_data": json_result})  # text[0] is the Document
                    
                return unified_responses

            synonym_responses = await process_synonyms_concurrently(close_synonyms_sorted)
            self.add_event(self.requirement_id , 'progress_message', {
                'event_type': 'CBP',
                'request_id': self.requirement_id,
                'data': {'status': 'completed', 'message': f"Evaluation completed for query {count}.", 'query_idx': count}
            })
            return synonym_responses
    
    def master_evaluation_v3_sync(self, source_document_text, uploaded_text, synonyms_text, criteria, count, limit=3):

        #print(f"ORIGINAL UPLOADED TEXT DOCUMENT: {uploaded_text}")
        #high_score_texts_sorted = sorted(uploaded_text, key=lambda x: x[1], reverse=True)  # x[1] is the score
        #print('top 5 high scores text: ', high_score_texts_sorted)
        if uploaded_text:
            #print(f"FULL UPLOADED TEXT FOR {uploaded_text}...")
            # Extracting documents and sorting by score
            # def format_uplaod_documents(res):
            #     formatted_results = []
            #     for item in res[0]:
            #         text_content = item[0]
            #         score = item[1]
            #         source_doc = item[2] if len(item) > 2 else "Unknown Source"
                    
            #         formatted_results.append({
            #             "text_content": text_content,
            #             "UPLOADED_DOCUMENT_NAME": source_doc
            #         })
            #     return formatted_results

            final_results = [{
                        "source_map": {},
                        "source_list": [],
                        "questions": [""]
                    }]
            sources_list = []
            source_map = {}
            sources = uploaded_text
            for i, source in enumerate(sources[0]):
                source_map[i] = source[0]
                sources_list.append({
                    "id": i,
                    "content": source[0],
                    "score": source[1],
                    "section_id": i,
                    "source": source[2],
                    "page_number": source[3]
                })
                final_results[0]['source_map'] = source_map
                final_results[0]['source_list'] = sources_list
                sources = final_results
            
            uploaded_text = sources[0]['source_list']
            #print(  'uploaded text: ', uploaded_text)
            #high_score_texts_sorted = sorted(uploaded_text, key=lambda x: x["score"], reverse=True)
            high_score_texts_sorted = [uploaded_text]

            #save  uploaded_text as json
            # with open(f"uploaded_text_{self.requirement_id}.json", "w") as f:
            #     json.dump(uploaded_text, f, indent=4)

              # x[1] is the score
            # print('top high scores text: ', high_score_texts_sorted)'''

        def process_texts_concurrently_sync(high_texts):
            tasks = [
                gevent.spawn(
                    self.chunck_processor.get_claude_to_evaluate_chunks_based_on_criteria_sync,
                    high_text, source_document_text, criteria, count  # high_text[0] is the Document
                ) for high_text in high_texts
            ]
            
            gevent.joinall(tasks, timeout=300)  # 5 minute timeout
            unified_responses = []
            for text, greenlet in zip(high_texts, tasks):
                if not greenlet.successful():
                    print(f"Error processing high texts: {greenlet.exception}")
                    continue
                json_result = greenlet.value
                unified_responses.append({"text_content": text, "evaluation_data": json_result})  # text[0] is the Document
                
            return unified_responses

        
        high_text_responses = process_texts_concurrently_sync(high_score_texts_sorted)
        print('high text responses: ', high_text_responses)
        print(f"TOTAL HIGH TEXT:{len(high_text_responses)}")
        
        self.add_event(self.requirement_id , 'progress_message', {
            'event_type': 'CBP',
            'request_id': self.requirement_id,
            'data': {'status': 'completed', 'message': f"Evaluation completed for query {count}.", 'query_idx': count}
        })

        return high_text_responses
         

        # If synonyms have high similarity, process them
        if synonyms_text:
            synonyms_text = format_uplaod_documents(synonyms_text)
            # Sort the close score synonyms by the highest score in descending order
            # close_synonyms_sorted = sorted(synonyms_text, key=lambda x: x[1], reverse=True)  # x[1] is the score
            all_results = []
            for sublist in synonyms_text:
                all_results.extend(sublist)

            close_synonyms_sorted = sorted(uploaded_text, key=lambda x: x["score"], reverse=True)
            close_synonyms_sorted=[close_synonyms_sorted]
            print('synonyms top 5: ', close_synonyms_sorted)

            def process_synonyms_concurrently_sync(synonyms):
                tasks = [
                    gevent.spawn(
                        self.chunck_processor.get_claude_to_evaluate_chunks_based_on_criteria_sync,
                        synonym[0], source_document_text, criteria, count  # synonym[0] is the Document
                    ) for synonym in synonyms
                ]
                
                gevent.joinall(tasks, timeout=300)  # 5 minute timeout
                unified_responses = []
                for text, greenlet in zip(synonyms, tasks):
                    if not greenlet.successful():
                        print(f"Error processing synonym: {greenlet.exception}")
                        continue
                    json_result = greenlet.value
                    unified_responses.append({"text_content": text[0], "evaluation_data": json_result})  # text[0] is the Document
                    
                return unified_responses

            synonym_responses = process_synonyms_concurrently_sync(close_synonyms_sorted)
            self.add_event(self.requirement_id , 'progress_message', {
                'event_type': 'CBP',
                'request_id': self.requirement_id,
                'data': {'status': 'completed', 'message': f"Evaluation completed for query {count}.", 'query_idx': count}
            })
            return synonym_responses
        


    async def extract_data_from_string(self, data):
        print('this is data given to extract from string: ', data)
        data_string = data if self.type == "claude" else data.choices[0].message.content
        patterns = {tag: re.compile(fr"<{tag}>(.*?)</{tag}>", re.DOTALL | re.IGNORECASE) for tag in ["Evaluation", "Score", "Content", "Reason", "Reference"]}
        return [patterns[tag].search(data_string).group(1) if patterns[tag].search(data_string) else '' for tag in patterns]
    
    async def extract_data_from_string_v2(self, data):
        print('this is data given to extract from string: ', data)
        data_string = data if self.type == "claude" else data.choices[0].message.content
        patterns = {
        "Evaluation": re.compile(r"\*\*Evaluation:\*\* (.*?)\n", re.DOTALL | re.IGNORECASE),
        "Score": re.compile(r"\*\*Score:\*\* (.*?)\n", re.DOTALL | re.IGNORECASE),
        "Reason": re.compile(r"\*\*Reason:\*\* (.*?)\n", re.DOTALL | re.IGNORECASE),
        "Reference": re.compile(r"\*\*Reference:\*\* (.*?)\n", re.DOTALL | re.IGNORECASE)
        }
        return {tag: (patterns[tag].search(data_string).group(1) if patterns[tag].search(data_string) else '') for tag in patterns}
    

    def add_event(self, request_id, event_name, data):
        print(f"Adding event {event_name} to room {request_id}")
        if self.socket_manager:
        # return
           self.socket_manager.emit_to_client(request_id, event_name, data)
