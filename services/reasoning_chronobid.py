import gevent
from gevent import monkey
monkey.patch_all()

import time
import json, sys
from services.open_ai_service import <PERSON><PERSON><PERSON><PERSON>
from services.claude_ai_service import Claude<PERSON>ervice
from services.prompt_saver import PromptSaver
from services.prompt_loader import PromptLoader
import asyncio
from typing import Dict, List, Tuple, Any, Optional
import re
import os
# import eventlet
from pydantic import BaseModel


class ResponseInfo(BaseModel):
            response: str

class ChronobidReasoning:
    def __init__(self):
        self.source_map = {}
        self.open_ai_manager = AssistantManager()


        
        self.claude_client = ClaudeService(claude_keys=['CLAUDE_API_CBP', 'CLAUDE_API_CBP'],default_key="CLAUDE_API_CBP")
        self.prompt_saver = PromptSaver()

       
           
    
    def save_reasoning_prompts(self):
        self.prompt_saver.save_prompt()
    
    async def generate_section(self, prompt, model="Claude 3 Sonnet", temperature=0.001):
        messages = [
            {
                "role": "user",
                "content": prompt
            }
        ]
        try:
            completion = await self.claude_client.generate_message_agent_sonnet(
                messages=messages,
                temperature=temperature,
            )
            if completion and hasattr(completion, 'content') and len(completion.content) > 0:
                response = completion.content[0].text
                token_count_in = completion.usage.input_tokens
                token_count_out = completion.usage.output_tokens
            else:
                raise ValueError("Invalid response received")
        except Exception as e:
            print(e)
            raise ValueError("Error during message generation")
        
        return response, token_count_in, token_count_out

    async def get_weakness_section(self, text, criteria, count, log):
        self.prompt_loader = PromptLoader()
        criteria_data = f"• Criteria {count}: {criteria['name']}\n • Description: {criteria['description']}"
        formatted_prompt = self.prompt_loader.get_prompt('cbp_get_weakness_section', {"uploaded_document": text, "criteria": criteria_data, "count": count})
       
        # print('this is format: ', formatted_prompt)
        weak_text, token_count_in, token_count_out = await self.generate_section(formatted_prompt)
        log['evaluation_report_section']['weak_text'] += f"<p>{weak_text}</p>"
        log['input_token'] += token_count_in
        log['output_token'] += token_count_out
        # print('This is weak text: ',weak_text)

    async def get_strength_section(self, text, criteria, count, log):
        self.prompt_loader = PromptLoader()
        criteria_data = f"• Name: {criteria['name']} Description: {criteria['description']}"
        formatted_prompt = self.prompt_loader.get_prompt('cbp_get_strength_section', {"uploaded_document": text, "criteria": criteria_data, "count": count})
      
        strength_text, token_count_in, token_count_out = await self.generate_section(formatted_prompt)
        log['evaluation_report_section']['strength_text'] += f"<p>{strength_text}</p>"
        log['input_token'] += token_count_in
        log['output_token'] += token_count_out

    async def get_risk_section(self, text, criteria, count, log):
        self.prompt_loader = PromptLoader()
        criteria_data = f"• Criteria {count}: {criteria['name']}\n Description: {criteria['description']}"
        formatted_prompt = self.prompt_loader.get_prompt('cbp_get_risk_section', {"uploaded_document": text, "criteria": criteria_data, "count": count})
       
        risk_text, token_count_in, token_count_out = await self.generate_section(formatted_prompt)
        log['evaluation_report_section']['risk_text'] += f"<p>{risk_text}</p>"
        log['input_token'] += token_count_in
        log['output_token'] += token_count_out
    
    async def get_introduction_section(self, project_name, project_scope, criteria_list, log):
        print('getting intro section now...')
        self.prompt_loader = PromptLoader()
        criteria_merged = "\n".join([f"• {criteria['name']} {criteria['description']}" for criteria in criteria_list])
        formatted_prompt = self.prompt_loader.get_prompt('cbp_get_introduction_section', {"project_name": project_name, "project_scope": project_scope, "criteria_list": criteria_merged})
       
        intro_text, token_count_in, token_count_out = await self.generate_section(formatted_prompt)
        log['evaluation_report_section']['introduction'] += intro_text
        log['input_token'] += token_count_in
        log['output_token'] += token_count_out 
    
    async def get_cover_page_section(self, project_name, tender_title, tender_number, bidder_name, bidder_date, log):
        self.prompt_loader = PromptLoader()
        # Format the prompt for the cover page
        formatted_prompt = self.prompt_loader.get_prompt('cbp_get_cover_page_section', {
            "project_name": project_name,
            "tender_title": tender_title,
            "tender_number": tender_number,
            "bidder_name": bidder_name,
            "bidder_date": bidder_date
        })

        print(formatted_prompt)
        print('formatted prompt above...')

        # Generate the cover page content
        cover_page_text, token_count_in, token_count_out = await self.generate_section(formatted_prompt)

        # Update log for cover page section and token count
        log['evaluation_report_section']['cover_page'] = cover_page_text
        log['input_token'] += token_count_in
        log['output_token'] += token_count_out

    

    async def get_detailed_evaluation(self, text, criteria, count, log):
        self.prompt_loader = PromptLoader()
        criteria_data = f"• {criteria['name']} - {criteria['description']}"
        formatted_prompt = self.prompt_loader.get_prompt('cbp_get_detailed_evaluation', {"uploaded_document": text, "criteria": criteria_data, "count": count, "weight": criteria['weight']})
     
        print('Formatted prompt in detailed eval...')
        evaluation_text, token_count_in, token_count_out = await self.generate_section(formatted_prompt)
        print('evaluation_text....')
        evaluation_text = evaluation_text.replace('\n', '')
        log['evaluation_report_section']['detailed_evaluation'] += f"<p>{evaluation_text}</p>"
        # log['input_token'] += token_count_in
        # log['output_token'] += token_count_out
        
    async def get_recommendation(self, strength, weak, risk, bidder_name, project_name, criteria_list, log):
        self.prompt_loader = PromptLoader()
        criteria_merged = "\n".join([f"• Name: {criteria['name']} Description: {criteria['description']}" for criteria in criteria_list])
        formatted_prompt = self.prompt_loader.get_prompt('cbp_get_recommendation', {"risk": risk, "strength":strength, "weak": weak, "criteria": criteria_merged, "bidder_name":bidder_name, "project_name": project_name})
        
        recommendation_text, token_count_in, token_count_out = await self.generate_section(formatted_prompt)
        log['evaluation_report_section']['recommendation'] += f"<p>{recommendation_text}</p>"
        log['input_token'] += token_count_in 
        log['output_token'] += token_count_out
   
    async def get_strength_weakness_risk_text(self, title, text, criteria, count, log):
        self.prompt_loader = PromptLoader()
        criteria_data = f"• Name: {criteria['name']} Description: {criteria['description']}"
        formatted_prompt = self.prompt_loader.get_prompt('cbp_get_strength_weakness_risk_text', {
            'uploaded_document': text,
            'criteria': criteria_data,
            'count': count,
            'weight': criteria['weight']
        })
        generated_text, token_count_in, token_count_out = await self.generate_section(formatted_prompt)
        log['evaluation_report_section'][title] += f"<p>{generated_text}</p>"
        log['input_token'] += token_count_in
        log['output_token'] += token_count_out
        
    async def get_strength_weakness_risk(self, title, text, intro, bidder_name, criteria, log):
        self.prompt_loader = PromptLoader()
        criteria_data = f"• Name: {criteria['name']} Description: {criteria['description']}"
        formatted_prompt = self.prompt_loader.get_prompt('cbp_get_strength_weakness_risk', {
            'text': text,
            'intro': intro,
            'title': title,
            'criteria': criteria_data,
            'bidder_name': bidder_name
        })
       
        intro_text, token_count_in, token_count_out = await self.generate_section(formatted_prompt)
        log['evaluation_report_section']['strength_weak_risk_chunk_intro'][title] = f"<br/><b>{title.capitalize()}</b></b><br/>{intro_text}"
        log['input_token'] += token_count_in 
        log['output_token'] += token_count_out

    async def generate_full_evaluation_report(self, title, log):
        self.prompt_loader = PromptLoader()
        introduction = log['evaluation_report_section'].get('introduction', '')
        evaluation_result = log['evaluation_report_section'].get('evaluation_result', '')
        # strength_weak_risk_chunk_intro = log['evaluation_report_section']['strength_weak_risk_chunk_intro']
        # strength_intro = strength_weak_risk_chunk_intro.get('Strengths', '')
        # weak_intro = strength_weak_risk_chunk_intro.get('Weaknesses', '')
        # risk_intro = strength_weak_risk_chunk_intro.get('Risks', '')
        # strength_text_section = log['evaluation_report_section'].get('strength_text', '')
        # weak_text_section = log['evaluation_report_section'].get('weak_text', '')
        # risk_text_section = log['evaluation_report_section'].get('risk_text', '')
        recommendation_text_section = log['evaluation_report_section'].get('recommendation_text', '')
        detailed_evaluation_text_section = log['evaluation_report_section'].get('detailed_evaluate_text', '')

        full_report = self.prompt_loader.get_prompt('cbp_full_report', {
            'introduction': introduction,
            'evaluation_result': evaluation_result,
            # 'strength_intro': strength_intro,
            # 'strength_text_section': strength_text_section,
            # 'weak_intro': weak_intro,
            # 'weak_text_section': weak_text_section,
            # 'risk_intro': risk_intro,
            # 'risk_text_section': risk_text_section,
            'detailed_evaluation_text_section': detailed_evaluation_text_section,
            'recommendation_text_section': recommendation_text_section
        })

        log['evaluation_report_section']['evaluation_report'] = self.replace_newlines_with_br(full_report)
        log['evaluation_report_section']['evaluation_report'] = self.replace_placeholder("BIDDER NAME", title, log['evaluation_report_section']['evaluation_report'])

        # print('this is full evaluation report: ', log['evaluation_report_section']['evaluation_report'])

    def replace_newlines_with_br(self, input_string):
        """
        Replace all newline characters in the input string with <br/>.
        
        :param input_string: The string to be processed.
        :return: The string with newline characters replaced by <br/>.
        """
        return input_string.replace('\n', '<br/>')

    def replace_placeholder(self, tag, value, text):
        """
        Replaces all occurrences of the given placeholder tag in the text with the specified value.
        
        Parameters:
        - tag (str): The placeholder tag to be replaced.
        - value (str): The value to replace the placeholder with.
        - text (str): The text containing the placeholder(s).
        
        Returns:
        - str: The text with the placeholder replaced by the given value.
        """
        return text.replace(f'[{tag}]', value)


    async def get_claude_to_summarize_evaluate_v2(self, log, criteria_object, count, steps=3):
        self.prompt_loader = PromptLoader()
        print('this is criteria_object: ', criteria_object)

        # Handle formula-based criteria if present
        if criteria_object.get('formula'):
            self.handle_formula_criteria(criteria_object, log.get('bidder_name', ''), log)
            return

        prompt = self.prompt_loader.get_prompt('cbp_get_claude_to_summarize_evaluate_v2')
        messages = prompt.copy()
        criteria = f"""
            Criteria {count} 
            Name: {criteria_object['name']}
            Description: {criteria_object['description']}
            Weight: {criteria_object['weight']}
            <br/>
        """

        print(f"Evaluation Summary Processing: {log['evaluate_summary_processing']} at count {count}")

        print('after criteria...')
        # print(log['evaluate_summary_processing'])
        # print("initial prompt message: ", messages)
        messages[0]['content'] = messages[0]['content'].format(
            evaluation_summary_processing=log['evaluate_summary_processing'][count-1],
            criteria=criteria
        )
        
        while steps > 0:
            steps -= 1
            try:
                completion = await self.claude_client.generate_message_agent_sonnet_new(
                    messages=messages,
                    temperature=0.001
                )
                if completion and hasattr(completion.content[0], 'text'):
                    response = completion.content[0].text
                    # token_count_in = completion.usage.input_tokens
                    # token_count_out = completion.usage.output_tokens
                    break
                else:
                    raise ValueError("Invalid response received")
            except Exception as e:
                print(e)
                gevent.sleep(10)
                # await asyncio.sleep(10)
        
        print('after message completion....')

        # Remove any newline characters
        log['evaluate_summary_chunk'][count-1] = response.replace('\n', '')
        log['evaluate_summary']  = "<div class='container'>" + log['top_text'] + ''.join(log['evaluate_summary_chunk']) + "</div>"
        print('here.....')
        weighted_score = await self.manual_calculate_total_weighted_score_2(log['evaluate_summary'])
        log['evaluate_summary_score'] = weighted_score
        print('here 2.....')
        log['evaluate_summary'] = self.replace_evaluation_summary_placeholder(log['evaluate_summary'], weighted_score)
        print('here 4.....')
        log['evaluate_summary_intro'] = self.replace_evaluation_summary_placeholder(log['evaluate_summary_intro'], weighted_score)

    def generate_criteria_summary(
            self,
            main_criteria: Dict[str, Any], 
            sub_criteria: Optional[List[Dict[str, Any]]], 
            evaluation_docs: Dict[str, Any],
            log: Dict[str, Any],
            criteria_index: int, 
            bidder_name: Optional[str] = None,
            max_retries: int = 3) -> None:
        """
        Generate a summary evaluation for main criteria and optional sub-criteria using Claude AI.
    
        Args:
            main_criteria (Dict[str, Any]): The main criteria object containing name, description, and weight
            sub_criteria (Optional[List[Dict[str, Any]]]): List of sub-criteria objects or None if no sub-criteria
            evaluation_docs (Dict[str, Any]): Dictionary containing evaluation texts for main and sub-criteria
            log (Dict[str, Any]): Log dictionary to store results
            bidder_name (str): Name of the bidder being evaluated
            criteria_index (int): Index of the criteria being processed (1-based)
            max_retries (int, optional): Maximum number of retries for API calls. Defaults to 3.
        """
        prompt_loader = PromptLoader()
        print(f"Processing criteria {criteria_index}: {main_criteria.get('name')}")

        # Handle formula-based criteria if present
        # Get the prompt template
        prompt_template = prompt_loader.get_prompt('cbp_get_claude_to_summarize_evaluate_v3')
        messages = prompt_template.copy()

        if isinstance(messages, str):
            messages = [{"role": "user", "content": messages}]
        
        # Format the main criteria information
        criteria_info = f"""
            Criteria {criteria_index} 
            Name: {main_criteria['name']}
            Description: {main_criteria['description']}
            Weight: {main_criteria['weight']}
            <br/>
        """
        
        # Format sub-criteria information if available
        sub_criteria_info = ""
        if sub_criteria:
            for i, sub in enumerate(sub_criteria, 1):
                # Handle formula-base
                    
                sub_criteria_info += f"""
                    Sub-Criteria {i}
                    Name: {sub.get('name', '')}
                    Description: {sub.get('description', '')}
                    Weight: {sub.get('weight', 0)}
                    <br/>
                """
        
        # Prepare the message content with all evaluation data
        messages[0]['content'] = messages[0]['content'].format(
            main_criteria_evaluate_summary_processing=evaluation_docs["main_criteria"],
            sub_criteria_evaluate_summary_processing=evaluation_docs["sub_criteria"],
            criteria=criteria_info,
            sub_criteria=sub_criteria_info
        )
        # Make API calls with retries
        retries_left = max_retries
        response = None
        

        while retries_left > 0:
            retries_left -= 1
            try:
                completion = self.claude_client.generate_final_summary_cbp(
                    messages=messages,
                    temperature=0.001

                )
                if completion and hasattr(completion.content[0], 'text'):
                    response = completion.content[0].text
                    break

                else:
                    raise ValueError("Invalid response received from Claude API")
            except Exception as e:
                print(f"Error calling Claude API: {e}")
                if retries_left > 0:
                    print(f"Retrying... {retries_left} attempts left")
                    gevent.sleep(30)
        
        if not response:
            print("Failed to generate summary after all retry attempts")
            return
        
        print('Summary generation completed successfully')
        
        print(f"Response from AI : {response}")
        # Process the response
      
        
        # Add debug prints
        print(f"Initial evaluate_summary_chunk length: {len(log['evaluate_summary_chunk'])}")
        print(f"Criteria index: {criteria_index}")
        
        # Make sure the list has enough elements
        if criteria_index > len(log['evaluate_summary_chunk']):
            # Extend the list if needed
            log['evaluate_summary_chunk'].extend([""] * (criteria_index - len(log['evaluate_summary_chunk'])))
            print(f"Extended list to length: {len(log['evaluate_summary_chunk'])}")
        
        # More debug prints
        print(f"Final evaluate_summary_chunk length: {len(log['evaluate_summary_chunk'])}")
        print(f"Trying to access index: {criteria_index-1}")
        
        # Store the response in the log (using 0-based indexing)
        
        log['evaluate_summary_chunk'][criteria_index-1] = response.replace('\n', '')
        print("Evaluate summary chunk: ", log['evaluate_summary_chunk'])
        
        # Update the full summary - ensure all items in the list are strings before joining
        summary_chunks = []
        for chunk in log['evaluate_summary_chunk']:
            if chunk is not None:
                summary_chunks.append(chunk)
            else:
                summary_chunks.append('')
                
        log['evaluate_summary'] = "<div class='container'>" + log.get('top_text', '') + ''.join(summary_chunks) + "</div>"
        
        # Calculate and update the weighted score
        weighted_score = self.manual_calculate_total_weighted_score_2_sync(log['evaluate_summary'])
        log['evaluate_summary_score'] = weighted_score

        print(f"Criteria {criteria_index} summary completed with score: {weighted_score}")
        
        # Replace placeholders with the actual score
        log['evaluate_summary'] = self.replace_evaluation_summary_placeholder(log['evaluate_summary'], weighted_score)
        log['evaluate_summary_intro'] = self.replace_evaluation_summary_placeholder(log.get('evaluate_summary_intro', ''), weighted_score)
        

        if main_criteria.get('formula'):
            self.handle_formula_criteria(main_criteria, bidder_name, log,response.replace('\n', ''))
            
        print(f"Criteria {criteria_index} summary completed with score: {weighted_score}")


    def generate_tender_docs_for_criteria(
            self,
            criteria: Dict[str, Any],
            chunks: str,
            criteria_index: Optional[int] = None,
            max_retries: int = 1) -> Optional[str]:
        """
        Extract tender requirements from documents based on specified criteria using Claude AI.

        Args:
            criteria (Dict[str, Any]): The criteria object containing name, description, and weight
            chunks (str): Document content to analyze for tender requirements
            criteria_index (Optional[int]): Index of the criteria being processed (1-based). If None, will not be included in output
            max_retries (int, optional): Maximum number of retries for API calls. Defaults to 3.

        Returns:
            Optional[str]: Extracted tender requirements text, or None if extraction failed
        """
        prompt_loader = PromptLoader()
        criteria_name = criteria.get('name', 'Unknown Criteria')

        class ResponseInfo(BaseModel):
            response: str
           

        if criteria_index:
            print(f"Processing criteria {criteria_index}: {criteria_name}")
        else:
            print(f"Processing criteria: {criteria_name}")

        print(f"Criteria object: {criteria}")
        print(f"Chunks length: {len(chunks) if chunks else 0}")
        print(f"Max retries: {max_retries}")

        # Format the criteria information
        if criteria_index:
            criteria_info = f"""
            Criteria {criteria_index}
            Name: {criteria['name']}
            Description: {criteria.get('description', '')}
            Weight: {criteria.get('weight', 0)}
            """
        else:
            criteria_info = f"""
            Name: {criteria['name']}
            Description: {criteria.get('description', '')}
            Weight: {criteria.get('weight', 0)}
            """

        # Get the prompt template for tender requirements extraction and format it
        try:
            print("Loading prompt template 'summarize_tender_requiremments'...")
            prompt = prompt_loader.get_prompt('summarize_tender_requiremments', {
                "document_content": chunks,
                "criteria_name": criteria_info
            })

            messages = [{"role": "user", "content": prompt}]
            print("Prompt loaded and formatted successfully")
            print(f"Prompt length: {len(prompt) if prompt else 0}")
            print(f"Messages created: {len(messages)} message(s)")

        except Exception as e:
            print(f"Error loading or formatting prompt template: {e}")
            print(f"Error type: {type(e).__name__}")
            print(f"Error details: {str(e)}")
            import traceback
            print(f"Full traceback: {traceback.format_exc()}")
            return None
 
 
        # Make API calls with retries
        retries_left = max_retries
        response = None

        print(f"Starting API calls with max_retries: {max_retries}")
        print(f"Initial retries_left: {retries_left}")

        if retries_left <= 0:
            print("ERROR: max_retries is 0 or negative - no API calls will be made!")
            print("Please set max_retries to a positive number (e.g., 3)")
            return None

        if isinstance(messages, str):
            messages = [{"role": "user", "content": messages}]
        print(f"STARTING COMPLETION:")
        while retries_left > 0:
            print(f"Attempting API call, retries_left: {retries_left}")
            retries_left -= 1

            
            try:
                completion = self.claude_client.generate_message_agent_sonnet_v2_sync(ResponseInfo,messages, 0.01,max_tokens=5120)
                print(f"COMPLETION: {completion}")
                
                if completion and hasattr(completion.content[0], 'input'):
                    response = completion.content[0].input
                    master_response = ResponseInfo(**response)
                    response = master_response.response
                    break
                else:
                    raise ValueError("Invalid response received from Claude API")

            except Exception as e:
                print(f"Error calling Claude API: {e}")
                print(f"Error type: {type(e).__name__}")
                print(f"Error details: {str(e)}")
                import traceback
                print(f"Full traceback: {traceback.format_exc()}")
                if retries_left > 0:
                    print(f"Retrying... {retries_left} attempts left")
                    time.sleep(10)  # Wait before retry
                else:
                    print("Failed to extract tender requirements after all retry attempts")
                    return None

        print(f"Final response check: response = {response}")
        if response:
            print(f"Successfully extracted tender requirements for criteria: {criteria_name}")
            print(f"Response length: {len(response)}")
            return response.strip()
        else:
            print(f"Failed to extract tender requirements for criteria: {criteria_name}")
            print("Response was None or empty")
            return None

    def generate_criteria_summaryv2(
            self,
            main_criteria: Dict[str, Any], 
            sub_criteria: Optional[List[Dict[str, Any]]], 
            evaluation_docs: Dict[str, Any],
            log: Dict[str, Any],
            criteria_index: int, 
            bidder_name: Optional[str] = None,
            max_retries: int = 3) -> None:
        """
        Generate a summary evaluation for main criteria and optional sub-criteria using Claude AI.
    
        Args:
            main_criteria (Dict[str, Any]): The main criteria object containing name, description, and weight
            sub_criteria (Optional[List[Dict[str, Any]]]): List of sub-criteria objects or None if no sub-criteria
            evaluation_docs (Dict[str, Any]): Dictionary containing evaluation texts for main and sub-criteria
            log (Dict[str, Any]): Log dictionary to store results
            bidder_name (str): Name of the bidder being evaluated
            criteria_index (int): Index of the criteria being processed (1-based)
            max_retries (int, optional): Maximum number of retries for API calls. Defaults to 3.
        """
        prompt_loader = PromptLoader()
        print(f"Processing criteria {criteria_index}: {main_criteria.get('name')}")

        # Handle formula-based criteria if present
       

        # Get the prompt template
        prompt_template = prompt_loader.get_prompt('cbp_get_claude_to_summarize_evaluate_v2')
        messages = prompt_template.copy()
        
        # Format the main criteria information
        criteria_info = f"""
            Criteria {criteria_index} 
            Name: {main_criteria['name']}
            Description: {main_criteria['description']}
            Weight: {main_criteria['weight']}
            <br/>
        """
        
        # Format sub-criteria information if available
        sub_criteria_info = ""
        if sub_criteria:
            for i, sub in enumerate(sub_criteria, 1):
                # Handle formula-base
                    
                sub_criteria_info += f"""
                    Sub-Criteria {i}
                    Name: {sub.get('name', '')}
                    Description: {sub.get('description', '')}
                    Weight: {sub.get('weight', 0)}
                    <br/>
                """
        
        # Prepare the message content with all evaluation data
        messages[0]['content'] = messages[0]['content'].format(
            main_criteria_evaluate_summary_processing=evaluation_docs["main_criteria"],
            sub_criteria_evaluate_summary_processing=evaluation_docs["sub_criteria"],
            criteria=criteria_info,
            sub_criteria=sub_criteria_info
        )
        # Make API calls with retries
        retries_left = max_retries
        response = None
        
        while retries_left > 0:
            retries_left -= 1
            try:
                completion = self.claude_client.generate_final_summary_cbp(
                    messages=messages,
                    temperature=0.001

                )
                if completion and hasattr(completion.content[0], 'text'):
                    response = completion.content[0].text
                    break
                else:
                    raise ValueError("Invalid response received from Claude API")
            except Exception as e:
                print(f"Error calling Claude API: {e}")
                if retries_left > 0:
                    print(f"Retrying... {retries_left} attempts left")
                    gevent.sleep(30)
        
        if not response:
            print("Failed to generate summary after all retry attempts")
            return
        
        print('Summary generation completed successfully')
        
        print(f"Response from AI : {response}")
        # Process the response
      
        
        # Add debug prints
        print(f"Initial evaluate_summary_chunk length: {len(log['evaluate_summary_chunk'])}")
        print(f"Criteria index: {criteria_index}")
        
        # Make sure the list has enough elements
        if criteria_index > len(log['evaluate_summary_chunk']):
            # Extend the list if needed
            log['evaluate_summary_chunk'].extend([""] * (criteria_index - len(log['evaluate_summary_chunk'])))
            print(f"Extended list to length: {len(log['evaluate_summary_chunk'])}")
        
        # More debug prints
        print(f"Final evaluate_summary_chunk length: {len(log['evaluate_summary_chunk'])}")
        print(f"Trying to access index: {criteria_index-1}")
        
        # Store the response in the log (using 0-based indexing)
        
        log['evaluate_summary_chunk'][criteria_index-1] = response.replace('\n', '')
        print("Evaluate summary chunk: ", log['evaluate_summary_chunk'])
        
        # Update the full summary - ensure all items in the list are strings before joining
        summary_chunks = []
        for chunk in log['evaluate_summary_chunk']:
            if chunk is not None:
                summary_chunks.append(chunk)
            else:
                summary_chunks.append('')
                
        log['evaluate_summary'] = "<div class='container'>" + log.get('top_text', '') + ''.join(summary_chunks) + "</div>"
        
        # Calculate and update the weighted score
        weighted_score = self.manual_calculate_total_weighted_score_2_sync(log['evaluate_summary'])
        log['evaluate_summary_score'] = weighted_score

        print(f"Criteria {criteria_index} summary completed with score: {weighted_score}")
        
        # Replace placeholders with the actual score
        log['evaluate_summary'] = self.replace_evaluation_summary_placeholder(log['evaluate_summary'], weighted_score)
        log['evaluate_summary_intro'] = self.replace_evaluation_summary_placeholder(log.get('evaluate_summary_intro', ''), weighted_score)
        

        if main_criteria.get('formula'):
            self.handle_formula_criteria(main_criteria, bidder_name, log,response.replace('\n', ''))
            
        print(f"Criteria {criteria_index} summary completed with score: {weighted_score}")


    def get_claude_to_summarize_evaluate_v2_sync(self, log, criteria_object, count, steps=3):
        self.prompt_loader = PromptLoader()
        print('this is criteria_object: ', count, criteria_object.get('name') )

        # Handle formula-based criteria if present
        if criteria_object.get('formula'):
            self.handle_formula_criteria(criteria_object, log.get('bidder_name', ''), log)
            return

        prompt = self.prompt_loader.get_prompt('cbp_get_claude_to_summarize_evaluate_v2')
        messages = prompt.copy()
        criteria = f"""
            Criteria {count} 
            Name: {criteria_object['name']}
            Description: {criteria_object['description']}
            Weight: {criteria_object['weight']}
            <br/>
        """

        # print(f"Evaluation Summary Processing: {log['evaluate_summary_processing']} at count {count}")

        print('after criteria...')
        # print(log['evaluate_summary_processing'])
        # print("initial prompt message: ", messages)
        messages[0]['content'] = messages[0]['content'].format(
            evaluation_summary_processing=log['evaluate_summary_processing'][count-1],
            criteria=criteria
        )
        
        while steps > 0:
            steps -= 1
            try:
                completion =  self.claude_client.generate_message_agent_sonnet_new_sync(
                    messages=messages,
                    temperature=0.001
                )
                if completion and hasattr(completion.content[0], 'text'):
                    response = completion.content[0].text
                    # token_count_in = completion.usage.input_tokens
                    # token_count_out = completion.usage.output_tokens
                    break
                else:
                    raise ValueError("Invalid response received")
            except Exception as e:
                print(e)
                gevent.sleep(30)
                # await asyncio.sleep(10)
        
        print('after message completion....')
        
        
        # Remove any newline characters
        log['evaluate_summary_chunk'][count-1] = response.replace('\n', '')
        log['evaluate_summary']  = "<div class='container'>" + log['top_text'] + ''.join(log['evaluate_summary_chunk']) + "</div>"
        print('here.....')
        weighted_score =  self.manual_calculate_total_weighted_score_2_sync(log['evaluate_summary'])
        log['evaluate_summary_score'] = weighted_score
        print('here 2.....')
        log['evaluate_summary'] = self.replace_evaluation_summary_placeholder(log['evaluate_summary'], weighted_score)
        print('here 4.....')
        log['evaluate_summary_intro'] = self.replace_evaluation_summary_placeholder(log['evaluate_summary_intro'], weighted_score)

    def extract_scores(self, content: str):
        match = re.search(r"<Score>(.*?)</Score>", content, re.DOTALL)
        return match.group(1).strip() if match else ""


    async def manual_calculate_total_weighted_score_2(self, text):
        # Clean up the input text to ensure uniformity
        text = text.replace('\n', '').replace('\r', '').strip()
        pattern = r'Weighted Score:\s*(\d+(\.\d+)?)%'

        matches = re.findall(pattern, text)        
        scores = [float(match[0]) for match in matches]
        
        total_weighted_score = sum(scores)
        print("total weighted score: ", total_weighted_score)
        
        average_weighted_score = total_weighted_score / len(scores) if scores else 0
        
        return total_weighted_score
    
    def manual_calculate_total_weighted_score_2_sync(self, text):

        
        # Clean up the input text to ensure uniformity
        text = text.replace('\n', '').replace('\r', '').strip()


        
        # Try multiple patterns to find weighted scores
        patterns = [
          
            r'<Main_weighted_score><h5 style="font-size: 14px;">Weighted Score:\s*(\d+(\.\d+)?)%</h5></Main_weighted_score>'
           
        ]
        scores = []
        for pattern in patterns:
            matches = re.findall(pattern, text)
            if matches:
                scores.extend([float(match[0]) for match in matches])
        
        if not scores:
            print("Warning: No weighted scores found in the text")
            print("Text sample:", text[:200])  # Print a sample of the text for debugging
            return 0
        
        total_weighted_score = round(sum(scores), 2)

        print(f"Found {len(scores)} scores: {scores}")
        print(f"Total weighted score: {total_weighted_score}")
        
        return total_weighted_score

    def replace_evaluation_summary_placeholder(self, evaluation_text, weighted_score):
        print('i am replacing eval score with new weighted score value: ', weighted_score)
        pattern = r"(<Overall_Score>)(.*?)(</Overall_Score>)"    
        replacement = f"<Overall_Score>{weighted_score:.1f}</Overall_Score>"
        updated_text = re.sub(pattern, replacement, evaluation_text)

        return updated_text
    
    def handle_formula_criteria(self, criteria: Dict[str, Any], bidder_name: str, log: Dict[str, Any], evaluation_response: str = None) -> None:
        """
        Handle criteria that have a formula field and update the log with formula-based evaluation.
        
        Args:
            criteria (Dict[str, Any]): The criteria object containing name, description, weight, and formula
            bidder_name (str): Name of the bidder being evaluated
            log (Dict[str, Any]): Log dictionary to store results
            evaluation_response (str): The actual evaluation response for this bidder
        """
        if not criteria.get('formula'):
            return
            
        # Initialize criteria_with_formula_eval if it doesn't exist
        if 'criteria_with_formula_eval' not in log:
            log['criteria_with_formula_eval'] = []
            
        # Create evaluation entry
        evaluation_entry = {
            "criteria": criteria['name'],
            "formula": criteria.get('formula', ''),
            "bid_evaluation": [{
                "bidder_name": bidder_name,
                "evaluation": evaluation_response.replace('\n', '') if evaluation_response else criteria.get('formula', '')
            }]
        }
        
        # Check if criteria already exists in the log
        existing_criteria = next(
            (item for item in log['criteria_with_formula_eval'] 
             if item['criteria'] == criteria['name']), 
            None
        )
        
        if existing_criteria:
            # Update existing criteria with new bidder evaluation
            existing_criteria['bid_evaluation'].append({
                "bidder_name": bidder_name,
                "evaluation": evaluation_response.replace('\n', '') if evaluation_response else criteria.get('formula', '')
            })
        else:
            # Add new criteria evaluation
            log['criteria_with_formula_eval'].append(evaluation_entry)

        

                        