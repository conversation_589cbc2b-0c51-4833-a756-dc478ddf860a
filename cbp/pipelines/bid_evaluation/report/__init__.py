import json
from .executive_summary import generate_executive_summary_html
from .technical_assessment import generate_technical_assessment
from .technical_recommendation import generate_technical_recommendation
from .overal_recommendation import generate_overall_recommendation
from .commercial_adjudication import generate_commercial_adjudication
from .technical_adjudication import generate_technical_adjudication
from .technical_criteria_analysis import generate_technical_criteria_analysis
from .bidder_scoring_matrix import process_scoring_matrix

class CbpReportGenerator:
    def __init__(self, data_cpb_output, commercial_data, technical_data):
        """
        Initialize the report generator with the required data.
        
        Args:
            data_cpb_output (dict): The CPB output data containing technical evaluations
            commercial_data (dict): The commercial data for pricing and other commercial aspects
            technical_data (dict): The technical data for technical adjudication
        """
        self.data_cpb_output = data_cpb_output
        self.commercial_data = commercial_data
        self.technical_data = technical_data
        self.reports = {}

    def generate_all_reports(self):
        """
        Generate all reports in sequence and return them as a dictionary.
        
        Returns:
            dict: Dictionary containing all generated reports
        """
        # Step 1: Generate Executive Summary
        self.reports['executive_summary'] = generate_executive_summary_html(self.commercial_data)

        self.reports["bidder_comparison_matrix"] = process_scoring_matrix(self.data_cpb_output)
        
        # Step 2: Generate Technical Assessment
        self.reports['technical_assessment'] = generate_technical_assessment(self.data_cpb_output)
        
        # Step 3: Generate Technical Recommendation

        self.reports["technical_criteria_analysis"] = generate_technical_criteria_analysis(self.data_cpb_output["data_aggregrated_report"]["technical_criteria_eval"])
        
        self.reports['technical_recommendation'] = generate_technical_recommendation(self.data_cpb_output)
        
        # Step 4: Generate Overall Recommendation
        self.reports['overall_recommendation'] = generate_overall_recommendation(self.data_cpb_output)
        
        # Step 5: Generate Attachments
        # Technical Adjudication
        self.reports['technical_adjudication'] = generate_technical_adjudication(self.technical_data)
        
        # Commercial Adjudication
        self.reports['commercial_adjudication'] = generate_commercial_adjudication(self.commercial_data)

        
        return self.reports

    def save_reports(self, output_dir="reports/cbp"):
        """
        Save all generated reports to a single HTML file.
        
        Args:
            output_dir (str): Directory where the report will be saved
        """
        import os
        
        # Create output directory if it doesn't exist
        os.makedirs(output_dir, exist_ok=True)
        
        # Create the combined HTML content
        html_content = """
        <!DOCTYPE html>
        <html>
        <head>
            <title>CPB Report</title>
            <style>
                body {
                    font-family: Arial, sans-serif;
                    line-height: 1.6;
                    margin: 40px;
                }
                .toc-container {
                    page-break-after: always;
                    margin-bottom: 40px;
                }
                .toc-title {
                    text-align: center;
                    font-size: 24px;
                    margin-bottom: 30px;
                    color: #2c3e50;
                }
                .toc-list {
                    list-style-type: none;
                    padding: 0;
                    margin: 0;
                }
                .toc-list li {
                    margin: 10px 0;
                    padding: 5px 0;
                    border-bottom: 1px solid #eee;
                }
                .toc-list a {
                    text-decoration: none;
                    color: #2c3e50;
                    font-size: 16px;
                    display: block;
                    padding: 5px 0;
                }
                .toc-list a:hover {
                    color: #3498db;
                }
                .section {
                    margin-top: 40px;
                    page-break-before: always;
                }
                .section:first-of-type {
                    page-break-before: avoid;
                }
            </style>
        </head>
        <body>
            <div class="toc-container">
                <h1 class="toc-title">Table of Contents</h1>
                <ul class="toc-list">
        """
        
        # Add table of contents links
        for report_name in self.reports.keys():
            section_id = report_name.replace('_', '-')
            display_name = report_name.replace('_', ' ').title()
            html_content += f'<li><a href="#{section_id}">{display_name}</a></li>\n'
        
        html_content += """
                </ul>
            </div>
        """
        
        # Add each report section
        for report_name, report_content in self.reports.items():
            section_id = report_name.replace('_', '-')
            display_name = report_name.replace('_', ' ').title()
            
            # Convert report content to string if it's a list
            if isinstance(report_content, list):
                report_content = "\n".join(str(item) for item in report_content)
            elif not isinstance(report_content, str):
                report_content = str(report_content)
            
            html_content += f"""
                <div id="{section_id}" class="section">
                    {report_content}
                </div>
            """
        
        html_content += """
        </body>
        </html>
        """
        
        # Save the combined report
        output_file = os.path.join(output_dir, "report.html")
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(html_content)
        print(f"Saved combined report to {output_file}")

    def combine_reports(self):
        """
        Save all generated reports to a single HTML file.
        
        Args:
            output_dir (str): Directory where the report will be saved
        """
    
        
        # Create the combined HTML content
        html_content = """
        <!DOCTYPE html>
        <html>
        <head>
            <title>CPB Report</title>
            <style>
                body {
                    font-family: Arial, sans-serif;
                    line-height: 1.6;
                    margin: 40px;
                }
                .toc-container {
                    page-break-after: always;
                    margin-bottom: 40px;
                }
                .toc-title {
                    text-align: center;
                    font-size: 24px;
                    margin-bottom: 30px;
                    color: #2c3e50;
                }
                .toc-list {
                    list-style-type: none;
                    padding: 0;
                    margin: 0;
                }
                .toc-list li {
                    margin: 10px 0;
                    padding: 5px 0;
                    border-bottom: 1px solid #eee;
                }
                .toc-list a {
                    text-decoration: none;
                    color: #2c3e50;
                    font-size: 16px;
                    display: block;
                    padding: 5px 0;
                }
                .toc-list a:hover {
                    color: #3498db;
                }
                .section {
                    margin-top: 40px;
                    page-break-before: always;
                }
                .section:first-of-type {
                    page-break-before: avoid;
                }
            </style>
        </head>
        <body>
            <div class="toc-container">
                <h1 class="toc-title">Table of Contents</h1>
                <ul class="toc-list">
        """
        
        # Add table of contents links
        for report_name in self.reports.keys():
            section_id = report_name.replace('_', '-')
            display_name = report_name.replace('_', ' ').title()
            html_content += f'<li><a href="#{section_id}">{display_name}</a></li>\n'
        
        html_content += """
                </ul>
            </div>
        """
        
        # Add each report section
        for report_name, report_content in self.reports.items():
            section_id = report_name.replace('_', '-')
            display_name = report_name.replace('_', ' ').title()
            
            # Convert report content to string if it's a list
            if isinstance(report_content, list):
                report_content = "\n".join(str(item) for item in report_content)
            elif not isinstance(report_content, str):
                report_content = str(report_content)
            
            html_content += f"""
                <div id="{section_id}" class="section">
                    {report_content}
                </div>
            """
        
        html_content += """
        </body>
        </html>
        """
        
        # Save the combined report
        return html_content
    


# Example usage:
if __name__ == "__main__":
    # Load your data
    with open("/Users/<USER>/Desktop/blessing_ai/mkd/ds_oil_and_gas/cbp/sample_docs/report.json", "r") as f:
        commercial_data = json.load(f)

    with open("/Users/<USER>/Desktop/blessing_ai/mkd/ds_oil_and_gas/cbp/sample_docs/tech.json", "r") as f:
        technical_data = json.load(f)

    data = {
        "data_aggregrated_report": {
            "comparison_table": [
                {
                    "criteria": "BALL MILL",
                    "Tysen": "<p>Tysen demonstrates strong technical compliance for the Ball Mill criterion, scoring 34% and ranking highest among the bidders. Their proposal excels in providing comprehensive power draw calculations, clearly defined liner specifications, and detailed lubrication system requirements. Compared to Mo group, Tysen's submission appears more thorough, particularly in areas such as liner specifications and lubrication systems, which were not explicitly mentioned in Mo group's summary. However, both bidders show robust compliance in key technical areas such as mill dimensions and power requirements.</p>",
                    "Mo group": "<p>Mo group achieves a score of 32% for the Ball Mill criterion, placing them slightly behind Tysen. Their proposal demonstrates strong compliance in key technical areas, providing detailed specifications for mill dimensions, power requirements, and operational parameters. Mo group's submission is particularly strong in detailing specific metrics such as mill size (6.6m ID, 9.8m effective grinding length) and power specifications (2 x 4,000 kW). However, unlike Tysen, Mo group's proposal lacks information on new liners, gas specifications, pipe details, and oil driller requirements, which prevents a perfect compliance score and gives Tysen a slight edge in overall completeness.</p>",
                    "Scores": {
                        "Tysen": "34%",
                        "Mo group": "32%"
                    }
                },
                {
                    "criteria": "Technical Criteria",
                    "Tysen": "<p>Tysen demonstrates strong technical competence, particularly in equipment specifications and ball mill documentation. Their submission includes detailed power calculations, dimensional specifications, and operational parameters, with clear performance guarantees for the ball mill. This comprehensive approach places Tysen slightly ahead in the technical criteria. However, there is room for improvement in the documentation of some auxiliary systems. Compared to Mo group, Tysen's submission appears more complete, especially in terms of performance guarantees and auxiliary system details, which likely contributes to their marginally higher score.</p>",
                    "Mo group": "<p>Mo group's technical submission shows a high level of compliance, particularly for the Ball Mill equipment. They provide detailed specifications for critical operational parameters, including dimensions, power requirements, and speed, supported by reference materials such as the Mill Information Sheet and technical specifications for the low-speed synchronous motor. While their overall technical competence is strong, especially in core operational aspects, they fall slightly behind Tysen in completeness. Mo group's submission lacks some specific details that Tysen provided, such as information on new liners, gas requirements, pipe details, and oil driller information, which may account for the small difference in their technical criteria scores.</p>",
                    "Scores": {
                        "Tysen": "42.5%",
                        "Mo group": "40%"
                    }
                }
            ],
            "technical_criteria_names": [
                "BALL MILL",
                "Technical Criteria",
                "BALL MILL",
                "Technical Criteria"
            ],
            "bidder_summary": [
                {
                    "criteria": "technical_evaluation_criteria",
                    "Tysen_score": "",
                    "Tysen_summary": "",
                    "Mo group_score": "",
                    "Mo group_summary": ""
                },
                {
                    "criteria": "Technical Criteria",
                    "Tysen_score": "42.5%",
                    "Tysen_summary": "The technical submission shows strong compliance in terms of equipment specifications, with detailed power calculations, dimensional specifications, and operational parameters. The ball mill specifications are well-documented with clear performance guarantees, though some auxiliary systems could benefit from more detailed documentation.",
                    "Mo group_score": "40%",
                    "Mo group_summary": "The technical evaluation reveals an 80% compliance rate for the Ball Mill equipment. The bidder has provided detailed specifications for the mill's critical operational parameters including dimensions, power requirements, and speed. The documentation is supported by reference materials including the Mill Information Sheet and technical specifications for the low-speed synchronous motor. Areas requiring additional information include specifications for new liners, gas requirements, pipe details, and oil driller information. Overall, the submission demonstrates strong technical competence in the core operational aspects of the Ball Mill equipment."
                },
                {
                    "criteria": "BALL MILL",
                    "Tysen_score": "34%",
                    "Tysen_summary": "The ball mill specifications demonstrate robust technical compliance with detailed power requirements, dimensional specifications, and operational parameters. Key strengths include comprehensive power draw calculations, clearly defined liner specifications, and detailed lubrication system requirements.",
                    "Mo group_score": "32%",
                    "Mo group_summary": "The Ball Mill equipment evaluation shows strong compliance in key technical areas. The bidder has provided detailed specifications for mill dimensions (6.6m ID, 9.8m effective grinding length), power requirements (2 x 4,000 kW), operational parameters (12.55 rpm, 75% critical speed), and mill type (shell supported, one chamber). The specifications are well-documented in the Mill Information Sheet and technical proposal documents. However, information is missing for new liners, gas specifications, pipe details, and oil driller requirements, which prevents a perfect compliance score."
                }
            ],
            "technical_criteria_eval": [
                {
                    "criteria_name": "Technical Criteria",
                    "name": "Mo group",
                    "evaluation": "Reason: \n                \n                    The evaluation of the Ball Mill equipment is based on the technical specifications provided in the bidder's documents. Since no specific tender requirements were provided for comparison, the assessment focuses on the completeness and adequacy of the information provided by the bidder.\n                \n\nKey Technical Specifications Provided:\n\n\n\nMill Dimensions and Type:\n\nInside Diameter (ID): 6.6 meters, as documented in the \"Mill Information Sheet_thyssenkrupp.xlsx\" and \"Proposal for SAG and Ball Mill - Rev1.pdf\"\nEffective Grinding Length: 9.8 meters, confirmed in both the \"Mill Information Sheet_thyssenkrupp.xlsx\" and \"Proposal for SAG and Ball Mill - Rev1.pdf\"\nMill Type: Shell supported, One chamber mill, as specified in \"Mill Information Sheet_thyssenkrupp.xlsx\" and \"3.2 Technical Specification - Low Speed Synchronous 13.07.2021.pdf\"\n\n\n\nPower and Performance Specifications:\n\nPinion Power Draw: 7840 kW at motor shaft, with noted preconditions in Performance Warranty, as documented in \"Mill Information Sheet_thyssenkrupp.xlsx\"\nPower Consumption: 2 x 4,000 kW (total 8,000 kW), as specified in \"Proposal for SAG and Ball Mill - Rev1.pdf\" and \"Mill Information Sheet_thyssenkrupp.xlsx\"\nMill Speed: 12.55 rpm, documented in \"Mill Information Sheet_thyssenkrupp.xlsx\" and \"3.2 Technical Specification - Low Speed Synchronous 13.07.2021.pdf\"\nCritical Speed (N.C.): 75% of critical speed, as noted in \"Mill Information Sheet_thyssenkrupp.xlsx\"\n\n\n\n\nMissing or Incomplete Information:\n\n\n\n                        No information was provided for the following components:\n                        \nNew Liners specifications\nGas requirements or specifications\nPipe details or specifications\nOil driller specifications\n\n\n\n\nDocumentation Quality:\n\n\n\n                        The bidder has provided detailed technical documentation through multiple sources:\n                        \n\"Mill Information Sheet_thyssenkrupp.xlsx\" - Contains comprehensive mill specifications\n\"Proposal for SAG and Ball Mill - Rev1.pdf\" - Provides additional confirmation of key parameters\n\"3.2 Technical Specification - Low Speed Synchronous 13.07.2021.pdf\" - Contains motor and operational specifications\n\n\n\n\n                    The Ball Mill specifications provided by the bidder demonstrate a thorough understanding of the core technical requirements for this equipment. The documentation is consistent across multiple reference documents, indicating careful preparation of the bid. The specified mill dimensions, power requirements, and operational parameters are clearly defined and appear to be appropriate for industrial applications.\n                \n\n                    However, the absence of information regarding new liners, gas specifications, pipe details, and oil driller requirements represents a gap in the technical submission. These components can be important for the complete operation, maintenance, and performance of the Ball Mill. The bidder should be requested to provide this missing information to ensure a comprehensive technical package.\n                \n\n                    Overall, the bidder has provided approximately 80% of the expected technical information for the Ball Mill equipment, with the core operational specifications well documented but several auxiliary components lacking detail."
                },
                {
                    "criteria_name": "Technical Criteria",
                    "name": "Tysen",
                    "evaluation": "Reason:\n                The ball mill technical specifications demonstrate comprehensive coverage of critical operational parameters:\n\nPower and Performance Specifications:\n                        \nPinion power draw ranges from 6,826 kW (new liners) to 7,600 kW (worn liners)\nInstalled power: 8,000 kW\nMotor power output: 7,001-7,795 kW depending on conditions\nSpecific energy consumption: 13.96 kWh/t\n\n\nDimensional Specifications:\n                        \nInside Shell Diameter: 6.50 m (21.3 ft)\nEffective Grinding Length: 9.80 m (32.2 ft)\nL:D Ratio: 1.51\nInternal diameter with new liners: 6.34 m (20.8 ft)\n\n\nOperational Parameters:\n                        \nMill speed: 12.6 rpm (75% of critical speed)\nLinear velocity: 823-824 fpm (4.2 m/s)\nRubber liner thickness: 80 mm new (updated from 70 mm)\nBacking rubber: 6 mm\n\n\nLubrication System:\n                        \nTotal oil flow: 185 lpm\nGearbox oil flow: 179 lpm maximum\nBearing supply: 3 lpm/Bearing\nCooling capacity: 82 kW\n\n\n\nKey Documentation References:\n\n\"Las Truchas - Ball Mill Size Rev1.pdf\" and Rev0 provide core dimensional and operational specifications\n\"3142228 - Las Truchas - AG and BM Power Draw and Conveyance Warranty\" documents detail power requirements and guarantees\n\"OR185-82-60 - Specification.pdf\" outlines lubrication system requirements\n\nAreas for Enhancement:\n\nAdditional detail on maintenance access provisions could be beneficial\nMore specific information on instrumentation and control systems would strengthen the submission\nFurther clarification on spare parts recommendations would be valuable"
                }
            ]
        }
    }
    
    # Create report generator instance
    report_generator = CbpReportGenerator(data_cpb_output, commercial_data, technical_data)
    
    # Generate all reports
    reports = report_generator.generate_all_reports()
    
    # Save reports to files
    report_generator.save_reports()


