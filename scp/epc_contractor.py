import sys
import os
import re
import asyncio
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from models import File, Project, Chunks, Tags, Requirement
from sqlalchemy import or_
import uuid
import json
import anthropic
from typing import Dict, List, Any
from services.data_handler import DataManager
import pandas as pd
from services.pinecone_vector_db import CanopyAI
import requests

class EPCContractor:
    def __init__(self, req_id, socket_manager, package_id, vendor_id, vendor_file_id):
        self.req_id = req_id
        self.socket_manager = socket_manager
        self.package_id = package_id
        self.vendor_id = vendor_id
        self.vendor_file_id = vendor_file_id
        self.cheat_sheets = {}
        self.working_doc = {}
        self.comparison_summary = {}
        self.ai_comparison_results = {}
        parent_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
        self.env_file_path = os.path.join(parent_dir, 'env.json')
        self.setup_agent()
        self.data_manager = DataManager()
        self.vec_db = CanopyAI()
        
        # Get package information
        package = Project.get_by(id=package_id, entity_type='tender')
        if not package:
            raise ValueError(f"Package with ID {package_id} not found")
        
        # Get files for package and specific vendor file
        self.package_files = File.get_by(project_id=package_id)
        self.vendor_files = File.get_by(project_id=vendor_id)
        self.vendor_file = File.get_by(id=vendor_file_id)[0]
        
        # Read the Area of Focus Excel file
        excel_path = os.path.join(os.path.dirname(__file__), 'Area of focus.xlsx')
        self.requirements_df = pd.read_excel(excel_path)

        self.section_ids = [chunk['id'] for chunk in Chunks.get_by(file_id=self.vendor_file['id'])]

    def load_env(self):
        with open(self.env_file_path, 'r') as f:
            return json.load(f)
        
    def setup_claude_client(self, api_key):
        self.client = anthropic.Anthropic(api_key=api_key)

    def setup_agent(self):
        env_data = self.load_env()
        self.setup_claude_client(env_data.get('CLAUDE_API_KEY', ''))

    def _extract_vdrl_code(self, file):
        """Extract VDRL code from file name or metadata"""
        file_name = file['name']
        match = re.search(r'-[A-Za-z]\d{2}-', file_name)
        if match:
            return match.group(0)[1:-1]
        return None

    def add_event(self, request_id, event_name, data):
        # return
        self.socket_manager.emit_to_client(request_id, event_name, data, '/epc')

    def get_areas_focus_by_vdrl(self, vdrl_code):
        """Get the areas of focus for a given VDRL code."""
        try:
            headers = {
                "x-project": "YWllbmVyZ3k6aHNxdHJ0cDdoc3dpamp6dWU3c2N3emV4ZDhodTRh",
                "Content-Type": "application/json", 
                "Authorization": ""
            }
            
            payload = {
                "email": "<EMAIL>",
                "password": "a123456",
                "role": "user"
            }

            url = "https://backend.aienergy-oilandgas.com/v3/api/custom/aienergy/area-focus/vendor-documents"
            
            response = requests.get(
                url,
                headers=headers,
                data=json.dumps(payload)
            )
            response.raise_for_status()
            data = response.json()
            
            # Search through the data to find matching VDRL code
            for item in data['list']['data']:
                if item['vdrl_code'] == vdrl_code:
                    return item['areas_focus']
            return None
        
        except Exception as e:
            return None
        
    def _get_requirements_for_vdrl(self, vdrl_code):
        """Get minimum requirements for a given VDRL code"""
        # Filter requirements dataframe for the given VDRL code

        areas_focus = self.get_areas_focus_by_vdrl(vdrl_code)

        if not areas_focus:
            vdrl_requirements = self.requirements_df[
                self.requirements_df['VDRL CODE'] == vdrl_code
            ]
            # Convert minimum requirements to list and handle empty case
            minimum_requirements = vdrl_requirements['MINIMUM REQUIREMENTS'].tolist()
        else:
            minimum_requirements = [areas_focus]
        
        # Generate expanded queries using Claude
        expanded_queries = []
        if minimum_requirements:
            prompt = f"""
            Given these minimum requirements for technical documentation:
            {minimum_requirements[0]}

            Extract and return only the areas of focus as a simple list.
            Each area should be on a new line.
            Do not include any additional text or explanations.
            """
            
            try:
                response = self.client.messages.create(
                    model="claude-3-5-sonnet-latest", 
                    max_tokens=500,
                    temperature=0.15,
                    messages=[{
                        "role": "user",
                        "content": prompt
                    }]
                )
                
                if response.content:
                    # Split response into individual areas of focus
                    expanded_queries = [q.strip() for q in response.content[0].text.split('\n') if q.strip()]
                    
            except Exception as e:
                # Fallback to original requirements
                expanded_queries = minimum_requirements

        # Combine original and expanded queries
        search_queries = expanded_queries
        
        return search_queries

    def compare_requirements(self):
        """Compare vendor responses against minimum requirements using vector search"""
        try:
            # Initialize results storage
            comparison_results = []
            
            # Extract VDRL code from file name or metadata
            vdrl_code = self._extract_vdrl_code(self.vendor_file)
            if not vdrl_code:
                return []
            
            # Get requirements for this VDRL code
            areas_of_focus = self._get_requirements_for_vdrl(vdrl_code)
            print(f"\n\n\nNumber of areas of focus: {len(areas_of_focus)}")
            # Process each requirement
            for area_of_focus in areas_of_focus:
                # Query tender documents
                tender_results = self.vec_db.get_data(self.package_id, [area_of_focus], limit=3)
                
                # Query vendor documents
                vendor_results = self.vec_db.get_data_by_section_ids(self.vendor_id, [area_of_focus], self.section_ids, limit=3)
                
                # Extract relevant content from results
                tender_content = []
                if tender_results and tender_results['metadatas']:
                    for metadata_list in tender_results['metadatas']:
                        for metadata in metadata_list:
                            if 'documents' in metadata:
                                tender_content.append(metadata['documents'])
                
                vendor_content = []
                if vendor_results and vendor_results['metadatas']:
                    for metadata_list in vendor_results['metadatas']:
                        for metadata in metadata_list:
                            if 'documents' in metadata:
                                vendor_content.append(metadata['documents'])
                
                # Prepare comparison result
                comparison = {
                    'area_of_focus': area_of_focus,
                    'file_name': self.vendor_file['name'],
                    'tender_content': tender_content,
                    'vendor_content': vendor_content,
                }
                
                comparison_results.append(comparison)
            
            return comparison_results
            
        except Exception as e:
            return []

    async def generate_evaluation_summary(self, comparison_results, j):
        """Generate a comparison analysis using Claude to evaluate the results (async version)"""
        try:
            prompt = """
            You are an expert in analyzing technical requirements and vendor responses.
            Please analyze the following comparison data and provide a detailed evaluation of the vendor's response to the tender requirements for every area of focus.
            
            Format your response in HTML with the following structure for every area of focus:
            
            <div class="reason highlight">
                <h3 style="margin: 8px 0">Area of Focus number: [Exact Area of Focus Name]</h3>
                <ul style="margin: 4px 0; padding-left: 20px">
                    <p style="margin: 4px 0">Score: <span class='score'><Score>X%</Score></span></p>
                    <p style="font-size: 14px; color: #006699; margin: 4px 0" class='criteria_<area_of_focus_name>_evaluation_summary'>[Concise summary of evaluation with key findings and recommendations]</p>
                    <p style="margin: 4px 0">Reason: 
                        [STICK TO THIS TEMPLATE FOR REASON] 
                        <p style="margin: 4px 0"><strong>PROMPT INSTRUCTIONS:</strong> Use this template to evaluate tender submissions against requirements. Be creative and adapt the structure, language, and specific examples to match your evaluation context. Follow this exact format:</p>
                        <hr style="margin: 8px 0">

                        <h2 style="margin: 8px 0">TEMPLATE FORMAT:</h2>

                        <h3 style="margin: 8px 0">Tender Requirements</h3>
                        <p style="margin: 4px 0">Start with a paragraph explaining what the tender/reference document requires. Then use numbered lists when necessary to break down specific requirements.</p>

                        <p style="margin: 4px 0"><em>Example structure:</em></p>
                        <p style="margin: 4px 0">The tender requirements says... [main requirement topic] that must encompass several critical elements to ensure [purpose/compliance/standards].</p>

                        <p style="margin: 4px 0">The required components include:</p>
                        <ol style="margin: 4px 0; padding-left: 20px">
                            <li>Requirement 1 - Brief description</li>
                            <li>Requirement 2 - Brief description</li>
                            <li>Requirement 3 - Brief description</li>
                            <li>Additional requirements as needed</li>
                        </ol>

                        <h3 style="margin: 8px 0">Vendor Submission</h3>
                        <p style="margin: 4px 0">Describe what the vendor actually submitted in their response. Reference the specific section where you found this information.</p>

                        <p style="margin: 4px 0"><em>Example structure:</em></p>
                        <p style="margin: 4px 0">The vendor submission in <strong>Section [X]</strong> addresses [what they covered] but demonstrates [overall assessment - partial compliance/gaps/strengths].</p>

                        <h3 style="margin: 8px 0">Gaps, Strengths, and Weaknesses</h3>
                        <p style="margin: 4px 0">Break this down into clear categories using both paragraphs and lists:</p>

                        <h4 style="margin: 8px 0">What They Did Well (Strengths):</h4>
                        <ol style="margin: 4px 0; padding-left: 20px">
                            <li>Strength 1 with explanation</li>
                            <li>Strength 2 with explanation</li>
                        </ol>

                        <h4 style="margin: 8px 0">Critical Gaps and Missing Elements:</h4>
                        <p style="margin: 4px 0">However, the submission fails to provide several essential elements:</p>
                        <ol style="margin: 4px 0; padding-left: 20px">
                            <li><strong>Gap Category 1:</strong>
                                <ol type="a" style="margin: 4px 0; padding-left: 20px">
                                    <li>Specific missing item 1</li>
                                    <li>Specific missing item 2</li>
                                    <li>Specific missing item 3</li>
                                </ol>
                            </li>
                            <li><strong>Gap Category 2:</strong>
                                <ol type="a" style="margin: 4px 0; padding-left: 20px">
                                    <li>Specific missing item 1</li>
                                    <li>Specific missing item 2</li>
                                </ol>
                            </li>
                        </ol>

                        <h3 style="margin: 8px 0">Overall Assessment and Conclusion</h3>
                        <p style="margin: 4px 0">Provide a summary paragraph that pulls everything together, stating the overall compliance level and the impact of the identified gaps.</p>

                        <p style="margin: 4px 0"><em>Example structure:</em></p>
                        <p style="margin: 4px 0">While the submitted [document type] demonstrates [positive aspects], it falls short of meeting the comprehensive requirements outlined in the tender specification. The identified deficiencies in [key areas] represent [level of risk/compliance issues] and [impact on overall evaluation].</p>
                    </p>
                </ul>
            </div>

            IMPORTANT FORMATTING RULES:
            - Title should be size 18
            - Section headers should be size 15
            - Body text should be 11-12 size
            - No text should be bold
            - Maintain exact HTML structure and tag hierarchy
            - Use exact tag names as provided
            - Include % symbol for all scores
            - For partial information scenarios, use the 'highlight-red' class
            - Keep exact casing of area of focus names as provided

            The analysis must include:
            * Comprehensive breakdown of ALL requirements from tender and vendor analysis for every area of focus
            * Exhaustive analysis of each equipment item, examining every relevant detail and component
            * Conduct thorough gap analysis between tender requirements and vendor submissions
            * Identify and document all gaps, discrepancies, and exceeding requirements
            * State exact requirements from tender document
            * Document which requirements vendor has met
            * List requirements that are missing or incomplete
            * Provide specific examples from vendor's response
            * Compare vendor submission against tender requirements to identify gaps
            * Include any relevant values, specifications, or timelines mentioned
            * Highlight any deviations from the required standards
            

**ADDITIONAL NOTES – PLEASE FOLLOW CAREFULLY(ALL CRITICAL):**
    * **HTML FORMAT:**

    * You **must follow the provided HTML template exactly**.
    * **Do not change any tag names**. Tags such as `<Main_weighted_score>`, `<h5>`, `<h6>`, etc., must be used **exactly as provided**.
    * **Very important:** Use the **correct HTML heading tags**:

        * If `<h5>` is used in the template, you must use `<h5>` — do **not replace it** with another tag.
        * If `<h6>` is used, use `<h6>` exactly — **do not substitute it**.
    * **Font sizes:**

        * Main Criteria: **18px**
        * Sub-Criteria Headers: **15px**
        * Sub-Headers: **14px**
        * Body Text: **12px**
    * **Maintain the exact HTML structure and tag hierarchy.** Do not add or remove wrappers, and do not modify tag types or names — this is **critical for proper data extraction**.

    NOTE: ENSURE THAT BULLED POINTS ARE WELL PRESENTED AND STRUCTURED

    * **EVALUATION SUMMARY SECTION:**

    * Follow the **exact structure and format** provided in the template.
    * **Do not modify the layout, tags, or formatting** in this section.

    * **DOCUMENT NAME REFERENCING (VERY CRITICAL):**
    * For **every evaluation point, reason, and reference**, reference the name of the document when necessary but do not necessayr make it like that is the only file you are checking since there are lot of files

    * **SUMMARY:**

    * Follow the template **without deviations**.
    * **Do not alter tag names, font sizes, or structure**.
    * Ensure **correct and consistent usage** of tags such as `<h5>`, `<h6>`, etc., as originally provided.
    * Always include **document name references** in all relevant sections.

    NOTE (FOLLOW CAREFULLY): Do no add introductory or concluding statement along side the general html response e.g "Fine below the generated html format or here is the generated html format above"
    
    NOTE(THESE ARE VERY CRITICAL):
        NOTE: When referencing criteria and sub-criteria (inside html), use the exact name as provided even if the spelling looks incorrect (leave it as it is)
        1. Do not change the casing if the name is capitalized.
           If the name is in lowercase, keep it lowercase.
           If it is mixed case, keep it mixed case.

   


    # CRITICAL RULE (THIS RULE COVERS THE EVALUATION WHEN TALKING ABOUT BOTH SOURCE AND UPLOADED DOCUMENTS)

    ## Core Requirement
    When talking about any evaluation, you MUST be highly specific and provide concrete examples with actual identifiers, numbers, and details rather than generic statements. This applies to ALL evaluation scenarios, not just equipment or documentation.

    ## What This Means

    ### ❌ WRONG - Vague and Generic
    - "Some items don't meet requirements"(what items? what requirements? and why?)
    - "Tender requirement requested for main equipments" (what requirements?)
    - "There are compliance issues" (what requirements?)
    - "Several aspects are non-compliant" (what aspects?)
    - "Issues were identified" (what issues?)
    - "Performance is inadequate" (what performance?)

    ANOTHER EXAMPLES - I ADDED UB BRACKETS DETAILS TO BE PROVIDED
    Cost of equipment and all necessary components [what components?e.g., main machinery, control systems, safety equipment]
        Pricing for spare parts, categorized as:
        Commissioning spare parts [What are commissioning spare parts? e.g., initial setup and testing components]
        1-year operational spare parts [what a 1-year operational spare parts? e.g., routine maintenance and replacement items]
        Capital spare parts [ what  capital spare parts? e.g., major components with long lead times]
        All line items must be fully priced [what line items? e.g., unit cost, quantity, total amount]
        Spare parts pricing must align with Project Requirements [e.g., specific budget allocations and cost thresholds]

    ### ✅ CORRECT - Specific with Examples
    - "Mill equipment tag RX-456 does not comply with tender requirement RX-789"
    - "Training session TS-45 only covered 12 hours but standard requires 16 hours minimum"
    - "Budget line item BL-203 shows $45,000 but approved amount was $52,000"
    - "Tender requirement TR-789 specifies minimum 5 years experience but candidate has only 3 years"
    - "Tender requirement TR-456 mandates ISO 9001:2015 certification but provided certificate is ISO 9001:2008"

    ## Detailed Examples Across ALL Categories

    ### Equipment & Technical
    **Instead of:** "Equipment doesn't meet specs"
    **Write:** "Centrifugal pump Model CP-500 delivers 180 m³/h but specification Sheet S-14 requires minimum 220 m³/h"

    ### Personnel & Training
    **Instead of:** "Staff qualifications insufficient"
    **Write:** "Operator John Smith (ID: OP-445) has Level 2 certification but position requires Level 3 per job description JD-890"

    ### Financial & Budget
    **Instead of:** "Costs exceed budget"
    **Write:** "Project Phase 3 actual cost $127,500 exceeds approved budget of $115,000 by $12,500 (10.9% overrun)"

    ### Timeline & Schedule
    **Instead of:** "Behind schedule"
    **Write:** "Milestone M-7 completed on Day 45 but contract Schedule CS-12 required completion by Day 38 (7-day delay)"

    ### Quality & Performance
    **Instead of:** "Quality issues found"
    **Write:** "Concrete batch CB-450 tested at 3,200 PSI compressive strength but specification requires minimum 4,000 PSI"

    ### Compliance & Regulatory
    **Instead of:** "Regulatory non-compliance"
    **Write:** "Safety procedure SP-201 missing required step 4.3 as mandated by OSHA standard 1926.95(a)"

    ### Process & Procedures
    **Instead of:** "Process not followed correctly"
    **Write:** "Invoice processing took 12 business days but SOP-Finance-04 requires completion within 5 business days"

    ### Location & Geographic
    **Instead of:** "Wrong location"
    **Write:** "Installation at Grid Reference GR-450-320 but approved site plan shows Grid Reference GR-455-325"

    ## Summary Structure Template

    When summarizing findings for ANY evaluation type, use this format:

    **Item:** [Specific subject/element/component with ID/reference]
    **Issue:** [Exact non-compliance with reference numbers/codes]
    **Requirement:** [Specific standard/specification/rule reference]
    **Gap:** [Quantified difference between actual vs required]

    ### Example Summaries Across Different Areas:

    #### Equipment Example:
    - **Item:** Reactor vessel RV-100A
    - **Issue:** Design pressure rated at 150 PSI
    - **Requirement:** Tender specification TS-205 requires 200 PSI minimum
    - **Gap:** 50 PSI shortfall from minimum requirement

    #### Personnel Example:
    - **Item:** Site supervisor Maria Garcia (Employee ID: SS-789)
    - **Issue:** Has 3 years experience in role
    - **Requirement:** Contract clause CC-12 requires minimum 5 years experience
    - **Gap:** 2 years short of minimum requirement

    #### Financial Example:
    - **Item:** Marketing campaign MC-2024-Q2
    - **Issue:** Actual spend $89,400
    - **Requirement:** Approved budget BG-445 allocated $75,000
    - **Gap:** $14,400 over budget (19.2% excess)

    #### Process Example:
    - **Item:** Customer complaint resolution CCR-1847
    - **Issue:** Resolution completed in 8 business days
    - **Requirement:** Service Level Agreement SLA-CS-01 requires 3 business days maximum
    - **Gap:** 5 days beyond required timeline

    ## Key Principles
    1. Always include specific identifiers (IDs, codes, numbers, names)
    2. Reference exact sources (contracts, specifications, standards, procedures)
    3. Quantify gaps with actual measurements/numbers/percentages
    4. Provide direct comparisons between "actual" vs "required"
    5. Avoid generalizations - every statement must be traceable to specific evidence
    6. Apply this specificity to ANY subject matter being evaluated


    CRITICAL REMINDER:
        - Enusure the summary part for both criteria and sub-criteria is not missing, detailed and captured the entire evaluation (mention values where necessary e.g prices, timelines, specifications  etc as this will be used for comparison) and follow the naming format specific strictly
        -  Remember to add %  symbol to the scores , all scores are in percentage
    NOTE: I have provided a template demonstrating how the reason section should be formatted. Please follow this format carefully and ensure proper structure. The reason section should include a balanced mix of paragraphs, sections, and bulleted lists to provide comprehensive and well-detailed evaluation points.
    

    FINALLY - WHAT DOES SUCESS MEAN FOR YOU IN THIS TASK?
        - DETAIILING YOUR POINTS MAKES YOU LOOK LIKE A PROFESSIONAL AND CREDIBLE
        - FOLLOW THE TEMPLATE STRICTLY (WELL SECTIONED FOR EVERY AREA OF FOCUS) - 
            1. What tender requrements
            2. Vendor submission (Strenghts)
            3. what Vendor is missing (Gaps)
            4. summary and conclusion
            5. all very detailed
            MAKE SURE EACH OF THIS SECTION IS NOT MISSING PLEASE    
                

            Here is the comparison data to analyze:
            """
            
            # Add comparison data to prompt
            for i, result in enumerate(comparison_results):
                prompt += f"\n\nArea of Focus {i+j+1}: {result['area_of_focus']}"
                prompt += f"\nVendor File: {result['file_name']}"
                prompt += f"\nTender Content: {' '.join(result['tender_content'])}"
                prompt += f"\nVendor Content: {' '.join(result['vendor_content'])}"
                prompt += "\n---"
            
    
            # Run the blocking Claude API call in a thread
            response = await asyncio.to_thread(
                self.client.messages.create,
                model="claude-3-5-sonnet-latest",
                temperature=0.1,
                max_tokens=8192,
                system="You are an expert in analyzing technical requirements, tender responses and vendor responses. Provide clear, detailed evaluations in a structured text format for every area of focus. Explain each point in detail.",
                messages=[
                    {"role": "user", "content": prompt}
                ]
            )
            return response.content[0].text
                
        except Exception as e:
            return f"Error generating evaluation: {str(e)}"

    async def generate_evaluation_summary_batched(self, comparison_results, batch_size=3):
        """Generate evaluation summaries in batches concurrently to avoid token limit issues."""
        total = len(comparison_results)
        tasks = []
        for i in range(0, total, batch_size):
            batch = comparison_results[i:i+batch_size]
            tasks.append(self.generate_evaluation_summary(batch, i))
        results = await asyncio.gather(*tasks)
        all_html = "".join(results)
        return all_html

    async def process_requirements_comparison(self):
        """Main method to process requirements comparison and generate analysis (async version)"""
        try:
            comparison_results = self.compare_requirements()
            if not comparison_results:
                return {
                    "error": "No comparison results generated"
                }
            analysis = await self.generate_evaluation_summary_batched(comparison_results, batch_size=3)
            print(f"Analysis: \n\n{analysis}")
            self.add_event(self.req_id, 'completed', {'message': analysis})
            return {
                "analysis": analysis
            }
        except Exception as e:
            return {
                "error": str(e)
            }

if __name__ == "__main__":
    contractor = EPCContractor("53dc535d-2325-4a5b-a01d-46e6dd3f53d5", None, "12345", "67890")
    results = contractor.process_requirements_comparison()
    try:
        with open("comparison_results.json", 'w') as f:
            json.dump(results, f, indent=2)
    except Exception as e:
        pass