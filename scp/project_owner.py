import sys
import os
import re
import asyncio  # Add asyncio import
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from models import File, Project, Chunks, Tags, Requirement
from sqlalchemy import or_
import uuid
import json
import anthropic
from typing import Dict, List, Any
from services.data_handler import DataManager
import pandas as pd
from services.pinecone_vector_db import CanopyAI
import requests

class EngineeringProjectOwner:
    def __init__(self, req_id, socket_manager, project_id, engineering_id, engineer_file_id):
        self.req_id = req_id
        self.socket_manager = socket_manager
        self.project_id = project_id
        self.engineering_id = engineering_id
        self.engineer_file_id = engineer_file_id
        self.cheat_sheets = {}
        self.working_doc = {}
        self.comparison_summary = {}
        self.ai_comparison_results = {}
        parent_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
        self.env_file_path = os.path.join(parent_dir, 'env.json')
        self.setup_agent()
        self.data_manager = DataManager()
        self.vec_db = CanopyAI()
        
        # Get files for both project and engineering
        self.project_files = File.get_by(project_id=project_id)
        self.engineering_files = File.get_by(project_id=engineering_id)
        self.engineering_file = File.get_by(project_id=engineering_id)[0]
        self.section_ids = [chunk['id'] for chunk in Chunks.get_by(file_id=self.engineering_file['id'])]
        print(f"Engineering files: {self.engineering_files}")
        # Read the Engineering Area of Focus Excel file
        excel_path = os.path.join(os.path.dirname(__file__), 'Eng Area of focus.xlsx')
        self.requirements_df = pd.read_excel(excel_path)

    def load_env(self):
        with open(self.env_file_path, 'r') as f:
            return json.load(f)
        
    def setup_claude_client(self, api_key):
        self.client = anthropic.Anthropic(api_key=api_key)

    def setup_agent(self):
        env_data = self.load_env()
        self.setup_claude_client(env_data.get('CLAUDE_API_KEY', ''))

    def add_event(self, request_id, event_name, data):
        self.socket_manager.emit_to_client(request_id, event_name, data, '/epo')

    def _extract_engineering_code(self, file):
        """Extract engineering code from file name or metadata"""
        file_name = file['name']
        document_type = re.search(r'-[A-Za-z0-9]{3}-', file_name)
        document_discipline = re.search(r'-[A-Z]{2}-', file_name)
        if document_type and document_discipline:
            return document_type.group(0)[1:-1], document_discipline.group(0)[1:-1]
        return None

    def get_areas_focus_by_vdrl(self, vdrl_code):
        """Get the areas of focus for a given VDRL code."""
        try:
            headers = {
                "x-project": "YWllbmVyZ3k6aHNxdHJ0cDdoc3dpamp6dWU3c2N3emV4ZDhodTRh",
                "Content-Type": "application/json", 
                "Authorization": ""
            }
            
            payload = {
                "email": "<EMAIL>",
                "password": "a123456",
                "role": "user"
            }

            url = "https://backend.aienergy-oilandgas.com/v3/api/custom/aienergy/area-focus/engineering-documents"
            
            response = requests.get(
                url,
                headers=headers,
                data=json.dumps(payload)
            )
            response.raise_for_status()
            data = response.json()
            
            # Search through the data to find matching VDRL code
            for item in data['list']['data']:
                if item['vdrl_code'] == vdrl_code:
                    return item['areas_focus']
            return None
            
        except Exception as e:
            print(f"Error occurred: {e}")
            return None
        
    def _get_requirements_for_engineering(self, document_type, document_discipline):
        """Get minimum requirements for a given engineering code"""
        
        # Clean whitespace from column names
        self.requirements_df.columns = self.requirements_df.columns.str.strip()

        # Filter by document type and check if discipline is present
        results = self.requirements_df[
            (self.requirements_df['Document Type'] == document_type) &
            (self.requirements_df['Disciplines'].fillna('').str.contains(document_discipline, case=False))
        ]

        # Get key information as list
        key_info = results[['Key information']].dropna().to_dict(orient='records')
        
        # Generate expanded queries using Claude
        expanded_queries = []
        if key_info and len(key_info) > 0:
            prompt = f"""
            Given these key information requirements for {document_type} in {document_discipline}:
            {key_info[0]['Key information']}

            Generate specific search queries that would help find relevant content in technical documents. 
            Focus on key technical terms and specifications.
            Format each query on a new line.
            Keep queries concise but descriptive.
            Do not include variations of technical terminology.
            """
            
            try:
                response = self.client.messages.create(
                    model="claude-3-haiku-20240307",
                    max_tokens=500,
                    temperature=0.15,
                    messages=[{
                        "role": "user",
                        "content": prompt
                    }]
                )
                
                if response.content:
                    # Split response into individual queries
                    expanded_queries = [q.strip() for q in response.content[0].text.split('\n') if q.strip()]
                    
            except Exception as e:
                print(f"Error generating expanded queries: {e}")
                # Fallback to original requirements
                expanded_queries = [info['Key information'] for info in key_info]

        return expanded_queries

    def compare_requirements(self):
        """Compare engineering responses against minimum requirements using vector search"""
        try:
            # Initialize results storage
            comparison_results = []
            
            # Get requirements for each engineering file

                # Extract engineering code from file name or metadata
            document_type, document_discipline = ('DCA', 'PP')

            
            # Get requirements for this engineering code
            requirements = self._get_requirements_for_engineering(document_type, document_discipline)
            
            # Process each requirement
            for requirement in requirements:
                # Create search query from requirement
                search_query = f"{requirement}"
                
                # Query project documents
                project_results = self.vec_db.get_data(self.project_id, [search_query], limit=3)
                
                # Query engineering documents
                engineering_results = self.vec_db.get_data_by_section_ids(self.engineering_id, [search_query], self.section_ids, limit=3)
                
                # Extract relevant content from results
                project_content = []
                if project_results and project_results['metadatas']:
                    for metadata_list in project_results['metadatas']:
                        for metadata in metadata_list:
                            if 'documents' in metadata:
                                project_content.append(metadata['documents'])
                
                engineering_content = []
                if engineering_results and engineering_results['metadatas']:
                    for metadata_list in engineering_results['metadatas']:
                        for metadata in metadata_list:
                            if 'documents' in metadata:
                                engineering_content.append(metadata['documents'])
                
                # Prepare comparison result
                comparison = {
                    'requirement': requirement,
                    'document_type': document_type,
                    'document_discipline': document_discipline,
                    'project_content': project_content,
                    'engineering_content': engineering_content,
                    # 'project_sources': len(project_content),
                    # 'engineering_sources': len(engineering_content)
                }
                
                comparison_results.append(comparison)
            
            return comparison_results
            
        except Exception as e:
            print(f"Error comparing requirements: {e}")
            return []

    async def generate_evaluation_summary(self, comparison_results, j):
        """Generate a comparison analysis using Claude to evaluate the results (async version)"""
        try:
            # Prepare the prompt for Claude
            prompt = """You are an expert in analyzing engineering requirements and responses.
            Please analyze the following comparison data and provide a detailed evaluation of the engineering team's response to the project requirements for every area of focus.
            
            Format your response in HTML with the following structure for every area of focus:
            
            <div style="margin: 0; padding: 0">
                <h3 style="margin: 8px 0">Area of Focus number: [Exact Area of Focus Name]</h3>
                <ul style="margin: 4px 0; padding-left: 20px">
                    <p style="margin: 4px 0">Score: <span class='score'><Score>X%</Score></span></p>
                    <p style="font-size: 14px; color: #006699; margin: 4px 0" class='criteria_<area_of_focus_name>_evaluation_summary'>[Concise summary of evaluation with key findings and recommendations]</p>
                    <p class='reason highlight' style="margin: 4px 0">Reason: 
                        [STICK TO THIS TEMPLATE FOR REASON] 
                        <p style="margin: 4px 0"><strong>PROMPT INSTRUCTIONS:</strong> Use this template to evaluate engineering submissions against project requirements. Be creative and adapt the structure, language, and specific examples to match your evaluation context. Follow this exact format:</p>
                        <hr style="margin: 8px 0">

                        <h2 style="margin: 8px 0">TEMPLATE FORMAT:</h2>

                        <h3 style="margin: 8px 0">Project Requirements</h3>
                        <p style="margin: 4px 0">Start with a paragraph explaining what the project requirements specify. Then use numbered lists when necessary to break down specific requirements.</p>

                        <p style="margin: 4px 0"><em>Example structure:</em></p>
                        <p style="margin: 4px 0">The project requirements specify... [main requirement topic] that must encompass several critical elements to ensure [purpose/compliance/standards].</p>

                        <p style="margin: 4px 0">The required components include:</p>
                        <ol style="margin: 4px 0; padding-left: 20px">
                            <li>Requirement 1 - Brief description</li>
                            <li>Requirement 2 - Brief description</li>
                            <li>Requirement 3 - Brief description</li>
                            <li>Additional requirements as needed</li>
                        </ol>

                        <h3 style="margin: 8px 0">Engineering Team's Submission</h3>
                        <p style="margin: 4px 0">Describe what the engineering team actually submitted in their response. Reference the specific section where you found this information.</p>

                        <p style="margin: 4px 0"><em>Example structure:</em></p>
                        <p style="margin: 4px 0">The engineering team's submission in <strong>Section [X]</strong> addresses [what they covered] but demonstrates [overall assessment - partial compliance/gaps/strengths].</p>

                        <h3 style="margin: 8px 0">Gaps, Strengths, and Weaknesses</h3>
                        <p style="margin: 4px 0">Break this down into clear categories using both paragraphs and lists:</p>

                        <h4 style="margin: 8px 0">What They Did Well (Strengths):</h4>
                        <ol style="margin: 4px 0; padding-left: 20px">
                            <li>Strength 1 with explanation</li>
                            <li>Strength 2 with explanation</li>
                        </ol>

                        <h4 style="margin: 8px 0">Critical Gaps and Missing Elements:</h4>
                        <p style="margin: 4px 0">However, the submission fails to provide several essential elements:</p>
                        <ol style="margin: 4px 0; padding-left: 20px">
                            <li><strong>Gap Category 1:</strong>
                                <ol type="a" style="margin: 4px 0; padding-left: 20px">
                                    <li>Specific missing item 1</li>
                                    <li>Specific missing item 2</li>
                                    <li>Specific missing item 3</li>
                                </ol>
                            </li>
                            <li><strong>Gap Category 2:</strong>
                                <ol type="a" style="margin: 4px 0; padding-left: 20px">
                                    <li>Specific missing item 1</li>
                                    <li>Specific missing item 2</li>
                                </ol>
                            </li>
                        </ol>

                        <h3 style="margin: 8px 0">Overall Assessment and Conclusion</h3>
                        <p style="margin: 4px 0">Provide a summary paragraph that pulls everything together, stating the overall compliance level and the impact of the identified gaps.</p>

                        <p style="margin: 4px 0"><em>Example structure:</em></p>
                        <p style="margin: 4px 0">While the submitted [document type] demonstrates [positive aspects], it falls short of meeting the comprehensive requirements outlined in the project specification. The identified deficiencies in [key areas] represent [level of risk/compliance issues] and [impact on overall evaluation].</p>
                    </p>
                </ul>
            </div>

            <div style="margin: 8px 0">
                <h3 style="margin: 8px 0">Overall Evaluation Summary</h3>
                <ul style="margin: 4px 0; padding-left: 20px">
                    <p style="margin: 4px 0">Total Score: <span class='score'><Score>X%</Score></span></p>
                    <p class='reason highlight' style="margin: 4px 0">[Comprehensive summary of all areas of focus evaluations, highlighting key strengths, weaknesses, and critical recommendations]</p>
                </ul>
            </div>

            IMPORTANT FORMATTING RULES:
            - Title should be size 18
            - Section headers should be size 15
            - Body text should be 11-12 size
            - No text should be bold
            - Maintain exact HTML structure and tag hierarchy
            - Use exact tag names as provided
            - Include % symbol for all scores
            - For partial information scenarios, use the 'highlight-red' class
            - Keep exact casing of area of focus names as provided

            The analysis must include:
            * Comprehensive breakdown of ALL requirements from project and engineering team analysis
            * Exhaustive analysis of each equipment item, examining every relevant detail and component
            * Conduct thorough gap analysis between project requirements and engineering team's submissions
            * Identify and document all gaps, discrepancies, and exceeding requirements
            * State exact requirements from project document
            * Document which requirements engineering team has met
            * List requirements that are missing or incomplete
            * Provide specific examples from engineering team's response
            * Compare submission against project requirements to identify gaps
            * Include any relevant values, specifications, or timelines mentioned
            * Highlight any deviations from the required standards

            # CRITICAL RULE (THIS RULE COVERS THE EVALUATION WHEN TALKING ABOUT BOTH SOURCE AND UPLOADED DOCUMENTS)

            ## Core Requirement
            When talking about any evaluation, you MUST be highly specific and provide concrete examples with actual identifiers, numbers, and details rather than generic statements. This applies to ALL evaluation scenarios, not just equipment or documentation.

            ## What This Means

            ### ❌ WRONG - Vague and Generic
            - "Some items don't meet requirements"(what items? what requirements? and why?)
            - "Project requirement requested for main equipments" (what requirements?)
            - "There are compliance issues" (what requirements?)
            - "Several aspects are non-compliant" (what aspects?)
            - "Issues were identified" (what issues?)
            - "Performance is inadequate" (what performance?)

            ### ✅ CORRECT - Specific with Examples
            - "Mill equipment tag RX-456 does not comply with project requirement RX-789"
            - "Training session TS-45 only covered 12 hours but standard requires 16 hours minimum"
            - "Budget line item BL-203 shows $45,000 but approved amount was $52,000"
            - "Project requirement PR-789 specifies minimum 5 years experience but candidate has only 3 years"
            - "Project requirement PR-456 mandates ISO 9001:2015 certification but provided certificate is ISO 9001:2008"

            ## Detailed Examples Across ALL Categories

            ### Equipment & Technical
            **Instead of:** "Equipment doesn't meet specs"
            **Write:** "Centrifugal pump Model CP-500 delivers 180 m³/h but specification Sheet S-14 requires minimum 220 m³/h"

            ### Personnel & Training
            **Instead of:** "Staff qualifications insufficient"
            **Write:** "Operator John Smith (ID: OP-445) has Level 2 certification but position requires Level 3 per job description JD-890"

            ### Financial & Budget
            **Instead of:** "Costs exceed budget"
            **Write:** "Project Phase 3 actual cost $127,500 exceeds approved budget of $115,000 by $12,500 (10.9% overrun)"

            ### Timeline & Schedule
            **Instead of:** "Behind schedule"
            **Write:** "Milestone M-7 completed on Day 45 but project Schedule PS-12 required completion by Day 38 (7-day delay)"

            ### Quality & Performance
            **Instead of:** "Quality issues found"
            **Write:** "Concrete batch CB-450 tested at 3,200 PSI compressive strength but specification requires minimum 4,000 PSI"

            ### Compliance & Regulatory
            **Instead of:** "Regulatory non-compliance"
            **Write:** "Safety procedure SP-201 missing required step 4.3 as mandated by OSHA standard 1926.95(a)"

            ### Process & Procedures
            **Instead of:** "Process not followed correctly"
            **Write:** "Invoice processing took 12 business days but SOP-Finance-04 requires completion within 5 business days"

            ### Location & Geographic
            **Instead of:** "Wrong location"
            **Write:** "Installation at Grid Reference GR-450-320 but approved site plan shows Grid Reference GR-455-325"

            ## Summary Structure Template

            When summarizing findings for ANY evaluation type, use this format:

            **Item:** [Specific subject/element/component with ID/reference]
            **Issue:** [Exact non-compliance with reference numbers/codes]
            **Requirement:** [Specific standard/specification/rule reference]
            **Gap:** [Quantified difference between actual vs required]

            ### Example Summaries Across Different Areas:

            #### Equipment Example:
            - **Item:** Reactor vessel RV-100A
            - **Issue:** Design pressure rated at 150 PSI
            - **Requirement:** Project specification PS-205 requires 200 PSI minimum
            - **Gap:** 50 PSI shortfall from minimum requirement

            #### Personnel Example:
            - **Item:** Site supervisor Maria Garcia (Employee ID: SS-789)
            - **Issue:** Has 3 years experience in role
            - **Requirement:** Project clause PC-12 requires minimum 5 years experience
            - **Gap:** 2 years short of minimum requirement

            #### Financial Example:
            - **Item:** Project campaign PC-2024-Q2
            - **Issue:** Actual spend $89,400
            - **Requirement:** Approved budget BG-445 allocated $75,000
            - **Gap:** $14,400 over budget (19.2% excess)

            #### Process Example:
            - **Item:** Customer complaint resolution CCR-1847
            - **Issue:** Resolution completed in 8 business days
            - **Requirement:** Service Level Agreement SLA-CS-01 requires 3 business days maximum
            - **Gap:** 5 days beyond required timeline

            ## Key Principles
            1. Always include specific identifiers (IDs, codes, numbers, names)
            2. Reference exact sources (contracts, specifications, standards, procedures)
            3. Quantify gaps with actual measurements/numbers/percentages
            4. Provide direct comparisons between "actual" vs "required"
            5. Avoid generalizations - every statement must be traceable to specific evidence
            6. Apply this specificity to ANY subject matter being evaluated

            CRITICAL REMINDER:
                - Ensure the summary part for both criteria and sub-criteria is not missing, detailed and captured the entire evaluation (mention values where necessary e.g prices, timelines, specifications etc as this will be used for comparison) and follow the naming format specific strictly
                - Remember to add % symbol to the scores, all scores are in percentage

            Here is the comparison data to analyze:
            """
            
            # Add comparison data to prompt
            for i, result in enumerate(comparison_results):
                prompt += f"\n\nArea of Focus {i+j+1}: {result['requirement']}"
                prompt += f"\nProject Content: {' '.join(result['project_content'])}"
                prompt += f"\nEngineering Content: {' '.join(result['engineering_content'])}"
                prompt += "\n---"
            
            # Run the blocking Claude API call in a thread
            response = await asyncio.to_thread(
                self.client.messages.create,
                model="claude-3-5-sonnet-latest",
                max_tokens=8192,
                temperature=0.1,
                system="You are an expert in analyzing engineering requirements and responses. Provide clear, detailed evaluations in a structured text format.",
                messages=[
                    {"role": "user", "content": prompt}
                ]
            )
            
            return response.content[0].text
                
        except Exception as e:
            print(f"Error generating evaluation: {e}")
            return f"Error generating evaluation: {str(e)}"

    async def generate_evaluation_summary_batched(self, comparison_results, batch_size=3):
        """Generate evaluation summaries in batches concurrently to avoid token limit issues."""
        total = len(comparison_results)
        tasks = []
        for i in range(0, total, batch_size):
            batch = comparison_results[i:i+batch_size]
            tasks.append(self.generate_evaluation_summary(batch, i))
        results = await asyncio.gather(*tasks)
        all_html = "".join(results)
        return all_html

    async def process_requirements_comparison(self):
        """Main method to process requirements comparison and generate analysis (async version)"""
        try:
            comparison_results = self.compare_requirements()

            if not comparison_results:
                return {
                    "error": "No comparison results generated"
                }
            
            analysis = await self.generate_evaluation_summary_batched(comparison_results, batch_size=3)
            self.add_event(self.req_id, 'completed', {'message': analysis})
            print(f"Analysis: {analysis}")
            return {
                "analysis": analysis
            }
            
        except Exception as e:
            print(f"Error in process_requirements_comparison: {e}")
            return {
                "error": str(e)
            }

if __name__ == "__main__":
    async def main():
        project_owner = EngineeringProjectOwner("53dc535d-2325-4a5b-a01d-46e6dd3f53d5", None, "project_id", "engineering_id")
        results = await project_owner.process_requirements_comparison()
        print(json.dumps(results, indent=2))
        # Store results in a JSON file
        output_file = f"engineering_comparison_results.json"
        try:
            with open(output_file, 'w') as f:
                json.dump(results, f, indent=2)
            print(f"Results saved to {output_file}")
        except Exception as e:
            print(f"Error saving results to file: {e}")

    # Run the async main function
    asyncio.run(main())