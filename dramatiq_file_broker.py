import gevent
from gevent import monkey
monkey.patch_all()
# import eventlet
# eventlet.monkey_patch()

import argparse
import asyncio
import os
import random
import sys
import json
import dramatiq
from dramatiq.middleware.asyncio import AsyncIO
from flask import Flask
from flask_melodramatiq import RabbitmqBroker
import logging
from dramatiq.results import Results
from dramatiq.results.backends import RedisBackend
from models import Project
from cbp.pipelines.auto_criteria import auto_detect_criteria
from socket_instance import socket_instance
from dramatiq_middleware import ActivityLoggingMiddleware
# from dramatiq.middleware.prometheus import Prometheus
# from dramatiq.brokers.rabbitmq import RabbitmqBroker




env_file_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'env.json')
print(env_file_path)
with open(env_file_path, 'r') as f:
    env_data = json.load(f)
        
RABBITMQ_URL = env_data.get("RABBITMQ_URL", 'guest:guest@localhost:5672/')
REDIS_URL = env_data.get("REDIS_URL", 'redis://localhost:6379/0')

FERNET_KEY = env_data.get("FERNET_KEY", '')

logger = logging.getLogger("dramatiq")

# Configure broker with proper middleware
broker = RabbitmqBroker(url=f"amqp://{RABBITMQ_URL}", config_prefix="DRAMATIQ_FILE_BROKER")

# Add middleware
result_backend = RedisBackend(url=REDIS_URL)
broker.add_middleware(Results(backend=result_backend))
broker.add_middleware(dramatiq.middleware.TimeLimit())
broker.add_middleware(dramatiq.middleware.Callbacks())
broker.add_middleware(ActivityLoggingMiddleware(env_data))
dramatiq.set_broker(broker)

    
@dramatiq.actor(
    queue_name="file_upload",
    priority=0,
    time_limit=36000000,  # 1 hour in milliseconds
    max_retries=3,
    store_results=True
)
def run_file_upload_task(dirtry, project_id, file_ids, file_names, req_id=None, is_retry=False,project_type=""):
    try:
        from init import app  # Import the actual app instance
        from services.file_uploader_v2 import Fileuploader

        # Get socket manager instance
        socket_manager = socket_instance.get_instance()
        # If request ID is provided, emit a test event
        if req_id:
            # Test emit
            socket_manager.emit_to_client(
                req_id,
                'test_event',
                {'message': 'Starting file upload processing'},
                namespace='/file_upload'
            )
        with app.app_context():  # Create a context using the imported app
            uploader = Fileuploader(dirtry, project_id, file_ids, file_names, flask_app=app, socket_manager=socket_manager, req_id=req_id, is_retry=is_retry,project_type=project_type)
            Project.update(id=project_id, upload_status="processing")
            greenlet = gevent.spawn(uploader.process_documents)
            gevent.joinall([greenlet], timeout=None)
            

            if not greenlet.successful():
                raise greenlet.exception or Exception("Greenlet failed")
            Project.update(id=project_id, upload_status="completed")

            criteria_greenlet = gevent.spawn(auto_detect_criteria, project_id=project_id)

            gevent.joinall([criteria_greenlet], timeout=120)  # timeout in seconds
            if not criteria_greenlet.successful():
                print("Criteria detection greenlet failed")
                
            else:
                detected_criteria = criteria_greenlet.value
                print(f"Detected criteria: {detected_criteria}")
                Project.update(id=project_id, auto_criteria_technical=json.dumps(detected_criteria))
                
    except Exception as e:
        logger.error("Error during file upload: %s", str(e))
        Project.update(id=project_id, upload_status="failed")
        if req_id and socket_manager:
            socket_manager.emit_to_client(
                req_id,
                'error_event',
                {'message': f'Error during file upload,please try again'},
                namespace='/file_upload'
            )
        raise e
    
    
@dramatiq.actor(
    queue_name="ineight_download_task",
    priority=0,
    time_limit=36000000,  # 1 hour in milliseconds
    max_retries=3,
    store_results=True
)
def ineight_download_task():
    try:
        # Import app at function level to avoid circular imports
        from services.ineight import InEightClient
        from init import app
        
        # Create application context
        with app.app_context():
            ineight_client = InEightClient(fernet_key=FERNET_KEY)
            res = ineight_client.handle_download()
            
            
            return res
    except Exception as e:
        logger.error("Error during EPC Contractor evaluation: %s", str(e))
        raise e
    
    
